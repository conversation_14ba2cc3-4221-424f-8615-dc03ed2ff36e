USE PrometheusDB;
GO

PRINT 'Starting Evaluations table migration...'

-- Check if ModuleID column exists
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Evaluations') AND name = 'ModuleID')
BEGIN
    PRINT 'Adding ModuleID column to Evaluations table...'
    ALTER TABLE dbo.Evaluations ADD ModuleID INT NULL;
    
    -- Update existing evaluations to be associated with the first module of their course
    UPDATE e 
    SET ModuleID = (
        SELECT TOP 1 ModuleID 
        FROM dbo.CourseModules cm 
        WHERE cm.CourseID = e.CourseID 
        ORDER BY cm.SortOrder
    )
    FROM dbo.Evaluations e;
    
    -- Make ModuleID NOT NULL after updating existing records
    ALTER TABLE dbo.Evaluations ALTER COLUMN ModuleID INT NOT NULL;
    
    -- Add foreign key constraint
    ALTER TABLE dbo.Evaluations ADD CONSTRAINT FK_Evaluations_CourseModules 
        FOREIGN KEY (ModuleID) REFERENCES dbo.CourseModules(ModuleID) ON DELETE CASCADE;
    
    PRINT 'ModuleID column added successfully.'
END
ELSE
BEGIN
    PRINT 'ModuleID column already exists.'
END

-- Check if SortOrder column exists
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Evaluations') AND name = 'SortOrder')
BEGIN
    PRINT 'Adding SortOrder column to Evaluations table...'
    ALTER TABLE dbo.Evaluations ADD SortOrder INT NOT NULL DEFAULT 0;
    
    -- Update existing evaluations with proper sort order within each module
    WITH EvaluationRanking AS (
        SELECT EvaluationID, 
               ROW_NUMBER() OVER (PARTITION BY ModuleID ORDER BY EvaluationID) as RowNum
        FROM dbo.Evaluations
    )
    UPDATE e 
    SET SortOrder = er.RowNum
    FROM dbo.Evaluations e
    INNER JOIN EvaluationRanking er ON e.EvaluationID = er.EvaluationID;
    
    PRINT 'SortOrder column added successfully.'
END
ELSE
BEGIN
    PRINT 'SortOrder column already exists.'
END

PRINT 'Evaluations table migration completed successfully.'
GO
