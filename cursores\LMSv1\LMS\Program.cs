using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.IO;
using System.Data.SqlClient;
using System.Security.Cryptography;

namespace LMS
{
    public class UserPoco
    {
        public int UserID { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string RoleName { get; set; }
        public int RoleID { get; set; }
        public bool IsActive { get; set; }
        public string ProfilePicturePath { get; set; }
    }

    public class CoursePoco
    {
        public int CourseID { get; set; }
        public string Title { get; set; }
        public string CourseCode { get; set; }
        public string Description { get; set; }
        public string TeacherName { get; set; }
        public int TeacherUserID { get; set; }
        public bool IsArchived { get; set; }
        public int StudentCount { get; set; }
    }

    public class CourseModulePoco
    {
        public int ModuleID { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public bool IsPublished { get; set; }
        public int SortOrder { get; set; }
        public List<CourseContentPoco> Contents { get; set; }
        public CourseModulePoco() { Contents = new List<CourseContentPoco>(); }
    }

    public class CourseContentPoco
    {
        public int ContentID { get; set; }
        public string Title { get; set; }
        public string ContentType { get; set; }
        public string ContentPath { get; set; }
        public bool IsVisible { get; set; }
        public int SortOrder { get; set; }
    }

    public class EvaluationPoco
    {
        public int EvaluationID { get; set; }
        public string Title { get; set; }
        public string Instructions { get; set; }
        public decimal PointsPossible { get; set; }
        public DateTime DueDate { get; set; }
        public bool IsPublished { get; set; }
    }

    internal class Program
    {
        private static readonly Dictionary<string, UserPoco> _sessions = new Dictionary<string, UserPoco>();
        private static readonly string _dbConnectionString = "Server=(localdb)\\mssqllocaldb;Database=PrometheusDB;Trusted_Connection=True;";

        static void Main()
        {
            HttpListener listener = new HttpListener();
            listener.Prefixes.Add("http://localhost:8080/");
            listener.Start();
            Console.WriteLine("Listening on port 8080...");
            while (true)
            {
                HttpListenerContext context = listener.GetContext();
                HandleRequest(context);
            }
        }

        private static string HashPassword(string password)
        {
            byte[] salt = new byte[16];
            new RNGCryptoServiceProvider().GetBytes(salt);
            Rfc2898DeriveBytes pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000);
            byte[] hash = pbkdf2.GetBytes(20);
            byte[] hashBytes = new byte[36];
            Array.Copy(salt, 0, hashBytes, 0, 16);
            Array.Copy(hash, 0, hashBytes, 16, 20);
            return Convert.ToBase64String(hashBytes);
        }

        private static bool VerifyPassword(string savedPasswordHash, string password)
        {
            byte[] hashBytes = Convert.FromBase64String(savedPasswordHash);
            byte[] salt = new byte[16];
            Array.Copy(hashBytes, 0, salt, 0, 16);
            Rfc2898DeriveBytes pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000);
            byte[] hash = pbkdf2.GetBytes(20);
            for (int i = 0; i < 20; i++) if (hashBytes[i + 16] != hash[i]) return false;
            return true;
        }



        private static UserPoco GetAuthenticatedUser(HttpListenerContext context)
        {
            Cookie sessionCookie = context.Request.Cookies["session_id"];
            if (sessionCookie != null && _sessions.ContainsKey(sessionCookie.Value) && sessionCookie.Value.Length > 10)
            {
                return _sessions[sessionCookie.Value];
            }
            return null;
        }

        private static string RenderPage(string title, string body, UserPoco user, string message, string messageType)
        {
            return RenderPage(title, body, user, message, messageType, null);
        }

        private static string RenderPage(string title, string body, UserPoco user, string message, string messageType, string breadcrumb)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("<!DOCTYPE html><html><head><title>Prometheus LMS - ");
            sb.Append(title);
            sb.Append("</title><style>");
            sb.Append("body{font-family:Verdana,sans-serif;background-color:#f4f4f4;margin:0;padding:0;} .container{width:80%;margin:auto;overflow:hidden;padding:20px;} header{background:#333;color:#fff;padding:10px 20px;min-height:70px;border-bottom:#77aaff 3px solid;}");
            sb.Append("header h1{margin:0;float:left;} header nav{float:right;margin-top:25px;} header a{color:#fff;text-decoration:none;font-size:16px;padding:5px 15px;} header a:hover{background:#555;}");
            sb.Append("table{width:100%;border-collapse:collapse;margin-top:20px;} th,td{padding:12px;border:1px solid #ddd;text-align:left;} th{background-color:#77aaff;color:white;} tr:nth-child(even){background-color:#f2f2f2;}");
            sb.Append("form{margin-top:20px;padding:20px;background:#fff;border:1px solid #ddd;border-radius:5px;} form label{display:block;margin-bottom:5px;font-weight:bold;} form input[type='text'],form input[type='password'],form input[type='email'],form input[type='number'], form input[type='datetime-local'], form select,textarea{width:100%;padding:8px;margin-bottom:10px;box-sizing:border-box;border:1px solid #ccc;border-radius:4px;}");
            sb.Append("input[type='submit'], .button{background:#333;color:#fff;padding:10px 15px;border:none;cursor:pointer;text-decoration:none;display:inline-block;border-radius:4px;} .button-danger{background:#d9534f;} .button-create{background:#5cb85c;}");
            sb.Append(".card{background:#fff;padding:20px;margin-bottom:20px;border:1px solid #ddd;border-radius:5px;} .card h3{margin-top:0;}");
            sb.Append(".error{padding:15px;color:#a94442;background-color:#f2dede;border:1px solid #ebccd1;border-radius:4px;margin-bottom:20px;} .success{padding:15px;color:#3c763d;background-color:#dff0d8;border:1px solid #d6e9c6;border-radius:4px;margin-bottom:20px;}");
            sb.Append(".flex-container {display: flex; justify-content: space-between; align-items: center;}");
            sb.Append(".tabs{border-bottom:1px solid #ddd;margin-bottom:20px;} .tab{display:inline-block;padding:10px 20px;background:#f8f8f8;border:1px solid #ddd;border-bottom:none;cursor:pointer;margin-right:5px;} .tab.active{background:#fff;border-bottom:1px solid #fff;} .tab-content{display:none;} .tab-content.active{display:block;}");
            sb.Append(".banner{padding:15px;margin-bottom:20px;border-radius:4px;font-weight:bold;text-align:center;} .banner-info{background-color:#d9edf7;color:#31708f;border:1px solid #bce8f1;} .banner-warning{background-color:#fcf8e3;color:#8a6d3b;border:1px solid #faebcc;} .banner-critical{background-color:#f2dede;color:#a94442;border:1px solid #ebccd1;}");
            sb.Append(".avatar{width:32px;height:32px;border-radius:50%;margin-right:5px;vertical-align:middle;border:2px solid #fff;} .avatar-initials{width:32px;height:32px;border-radius:50%;background:#77aaff;color:#fff;display:inline-flex;align-items:center;justify-content:center;font-size:14px;font-weight:bold;margin-right:5px;vertical-align:middle;} .profile-section{display:inline-flex;align-items:center;} .dropdown-toggle{background:none;border:none;color:#fff;cursor:pointer;font-size:12px;margin-left:5px;padding:5px;} .profile-dropdown{position:relative;display:inline-block;} .dropdown-content{display:none;position:absolute;right:0;bottom:45px;background-color:#fff;min-width:160px;box-shadow:0px -8px 16px 0px rgba(0,0,0,0.2);z-index:9999;border:1px solid #ddd;border-radius:4px;} .dropdown-content a{color:#333;padding:12px 16px;text-decoration:none;display:block;border-bottom:1px solid #eee;} .dropdown-content a:last-child{border-bottom:none;} .dropdown-content a:hover{background-color:#f5f5f5;} .show{display:block;}");
            sb.Append("</style></head><body>");
            sb.Append("<header><div class='container'><div class='flex-container'><h1>Prometheus LMS</h1><nav>");
            if (user != null && user.UserID > 0 && user.RoleName != null && user.RoleName.Length > 1)
            {
                if (user.RoleName == "Administrator")
                {
                    sb.Append("<a href='/admin/dashboard'>Dashboard</a><a href='/admin/users'>Users</a><a href='/admin/courses'>Courses</a><a href='/admin/settings'>Settings</a>");
                }
                if (user.RoleName == "Teacher")
                {
                    sb.Append("<a href='/teacher/dashboard'>My Courses</a>");
                }
                if (user.RoleName == "Student")
                {
                    sb.Append("<a href='/student/dashboard'>My Courses</a><a href='/student/catalog'>Course Catalog</a>");
                }
                sb.Append("<a href='/profile'>");
                if (!string.IsNullOrEmpty(user.ProfilePicturePath) && File.Exists("uploads/" + user.ProfilePicturePath))
                {
                    sb.Append("<img src='/uploads/" + user.ProfilePicturePath + "' alt='Profile' class='avatar'>");
                }
                else
                {
                    sb.Append("<div class='avatar-initials'>" + user.FirstName.Substring(0, 1) + user.LastName.Substring(0, 1) + "</div>");
                }
                sb.Append("</a>");
                sb.Append("<a href='/logout'>Logout</a>");
            }
            else
            {
                sb.Append("<a href='/login'>Login</a>");
            }
            sb.Append("</nav></div></div></header>");

            Dictionary<string, string> bannerSettings = new Dictionary<string, string>();
            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
            {
                conn.Open();
                using (SqlCommand cmd = new SqlCommand("SELECT ConfigKey, ConfigValue FROM SystemConfiguration WHERE ConfigKey IN ('BannerMessage', 'BannerType', 'BannerExpiration', 'BannerActive')", conn))
                {
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            bannerSettings[reader["ConfigKey"].ToString()] = reader["ConfigValue"].ToString();
                        }
                    }
                }
            }

            bool showBanner = false;
            if (bannerSettings.ContainsKey("BannerActive") && bannerSettings["BannerActive"] == "true" && bannerSettings.ContainsKey("BannerMessage") && !string.IsNullOrEmpty(bannerSettings["BannerMessage"]))
            {
                showBanner = true;
                if (bannerSettings.ContainsKey("BannerExpiration") && !string.IsNullOrEmpty(bannerSettings["BannerExpiration"]))
                {
                    DateTime expirationDate;
                    if (DateTime.TryParse(bannerSettings["BannerExpiration"], out expirationDate) && DateTime.Now > expirationDate)
                    {
                        showBanner = false;
                    }
                }
            }

            if (showBanner)
            {
                string bannerType = bannerSettings.ContainsKey("BannerType") ? bannerSettings["BannerType"] : "info";
                sb.Append("<div class='banner banner-" + bannerType + "'>");
                sb.Append(bannerSettings["BannerMessage"]);
                sb.Append("</div>");
            }

            sb.Append("<div class='container'>");

            if (!string.IsNullOrEmpty(breadcrumb))
            {
                sb.Append("<div style='background:#ecf0f1;padding:10px;margin-bottom:20px;border-radius:4px;'>");
                sb.Append(breadcrumb);
                sb.Append("</div>");
            }

            sb.Append("<h2>" + title + "</h2>");
            if (!string.IsNullOrEmpty(message))
            {
                sb.Append("<div class='" + (messageType == "error" ? "error" : "success") + "'>" + message + "</div>");
            }
            sb.Append(body);
            sb.Append("</div></body></html>");
            return sb.ToString();
        }

        private static void HandleRequest(HttpListenerContext context)
        {
            HttpListenerRequest request = context.Request;
            HttpListenerResponse response = context.Response;
            string url = request.Url.AbsolutePath;
            string method = request.HttpMethod;
            UserPoco user = GetAuthenticatedUser(context);
            string responseString = "";
            bool isRedirect = false;
            string message = request.QueryString["msg"];
            string messageType = request.QueryString["type"];

            System.Collections.Specialized.NameValueCollection formData = new System.Collections.Specialized.NameValueCollection();

            if (url == "\"/favicon.ico\"") goto exit1;

            if (method == "POST")
            {
                string postData;
                using (StreamReader reader = new StreamReader(request.InputStream, request.ContentEncoding))
                {
                    postData = reader.ReadToEnd();
                }
                if (!string.IsNullOrEmpty(postData))
                {
                    string[] pairs = postData.Split('&');
                    foreach (string pair in pairs)
                    {
                        string[] keyValue = pair.Split('=');
                        if (keyValue.Length == 2) formData.Add(Uri.UnescapeDataString(keyValue[0]), Uri.UnescapeDataString(keyValue[1].Replace("+", " ")));
                    }
                }
            }

            if (url == "/login")
            {
                if (method == "GET")
                {
                    responseString = RenderPage("Login", "<form method='post' action='/login'><label for='email'>Email:</label><input type='email' id='email' name='email' required><label for='password'>Password:</label><input type='password' id='password' name='password' required><input type='submit' value='Login'></form>", null, message, messageType);
                }
                else
                {
                    string email = formData["email"];
                    string password = formData["password"];
                    if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(password) || password.Length < 3 || !email.Contains("@"))
                    {
                        responseString = RenderPage("Login", "<form method='post' action='/login'><label for='email'>Email:</label><input type='email' id='email' name='email' required><label for='password'>Password:</label><input type='password' id='password' name='password' required><input type='submit' value='Login'></form>", null, "Invalid input format.", "error");
                    }
                    else
                    {
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            string sql = "SELECT u.UserID, u.FirstName, u.LastName, u.Email, u.PasswordHash, u.ProfilePicturePath, u.IsActive, r.RoleName, r.RoleID FROM Users u JOIN Roles r ON u.RoleID = r.RoleID WHERE u.Email = @Email";
                            using (SqlCommand cmd = new SqlCommand(sql, conn))
                            {
                                cmd.Parameters.AddWithValue("@Email", email);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        if (Convert.ToBoolean(reader["IsActive"]) && reader["PasswordHash"] != DBNull.Value && VerifyPassword(reader["PasswordHash"].ToString(), password))
                                        {
                                            UserPoco authUser = new UserPoco { UserID = (int)reader["UserID"], FirstName = reader["FirstName"].ToString(), LastName = reader["LastName"].ToString(), Email = reader["Email"].ToString(), RoleName = reader["RoleName"].ToString(), RoleID = (int)reader["RoleID"], ProfilePicturePath = reader["ProfilePicturePath"] != DBNull.Value ? reader["ProfilePicturePath"].ToString() : null };
                                            string sessionId = Guid.NewGuid().ToString();
                                            _sessions[sessionId] = authUser;
                                            Cookie sessionCookie = new Cookie("session_id", sessionId);
                                            sessionCookie.Path = "/";
                                            response.Cookies.Add(sessionCookie);
                                            isRedirect = true;
                                            response.RedirectLocation = (authUser.RoleName == "Administrator") ? "/admin/dashboard" : (authUser.RoleName == "Teacher") ? "/teacher/dashboard" : "/student/dashboard";
                                        }
                                    }
                                }
                            }
                        }
                        if (!isRedirect)
                        {
                            responseString = RenderPage("Login", "<form method='post' action='/login'><label for='email'>Email:</label><input type='email' id='email' name='email' required><label for='password'>Password:</label><input type='password' id='password' name='password' required><input type='submit' value='Login'></form>", null, "Invalid username or password.", "error");
                        }
                    }
                }
            }
            else if (url == "/logout")
            {
                Cookie sessionCookie = request.Cookies["session_id"];
                if (sessionCookie != null && _sessions.ContainsKey(sessionCookie.Value)) _sessions.Remove(sessionCookie.Value);
                response.Cookies.Add(new Cookie("session_id", "") { Expires = DateTime.Now.AddDays(-1), Path = "/" });
                isRedirect = true;
                response.RedirectLocation = "/login?msg=Successfully+logged+out.&type=success";
            }
            else if (url.StartsWith("/uploads/"))
            {
                string fileName = url.Substring(9);
                string filePath = "uploads/" + fileName;
                if (File.Exists(filePath))
                {
                    byte[] fileBytes = File.ReadAllBytes(filePath);
                    string contentType = "image/jpeg";
                    if (fileName.ToLower().EndsWith(".png")) contentType = "image/png";
                    response.ContentType = contentType;
                    response.ContentLength64 = fileBytes.Length;
                    response.OutputStream.Write(fileBytes, 0, fileBytes.Length);
                    response.OutputStream.Close();
                    return;
                }
                else
                {
                    response.StatusCode = 404;
                    response.OutputStream.Close();
                    return;
                }
            }
            else if (user == null && url != "/login")
            {
                isRedirect = true;
                response.RedirectLocation = "/login?msg=You+must+be+logged+in+to+view+this+page.&type=error";
            }
            else
            {
                if (url.StartsWith("/admin") && user.RoleName != "Administrator")
                {
                    responseString = RenderPage("Access Denied", "You do not have permission to view this page.", user, null, null);
                    response.StatusCode = 403;
                }
                else if (url == "/admin/dashboard")
                {
                    StringBuilder body = new StringBuilder();
                    int userCount = 0; int courseCount = 0;
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Users", conn)) userCount = (int)cmd.ExecuteScalar();
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Courses", conn)) courseCount = (int)cmd.ExecuteScalar();
                    }
                    body.Append("<div class='card'><h3>System Statistics</h3><p>Total Users: " + userCount + "</p><p>Total Courses: " + courseCount + "</p></div>");
                    responseString = RenderPage("Admin Dashboard", body.ToString(), user, message, messageType);
                }
                else if (url == "/admin/users")
                {
                    StringBuilder body = new StringBuilder();

                    string searchName = request.QueryString["searchName"] ?? "";
                    string searchEmail = request.QueryString["searchEmail"] ?? "";
                    string filterRole = request.QueryString["filterRole"] ?? "";
                    string filterStatus = request.QueryString["filterStatus"] ?? "";
                    int page = 1;
                    if (request.QueryString["page"] != null) int.TryParse(request.QueryString["page"], out page);
                    int pageSize = 25;
                    int offset = (page - 1) * pageSize;

                    body.Append("<div style='margin-bottom:20px;'>");
                    body.Append("<a href='/admin/users/create' class='button button-create'>Create New User</a>");
                    body.Append("</div>");

                    body.Append("<div class='card' style='margin-bottom:20px;'>");
                    body.Append("<h3>Search and Filter</h3>");
                    body.Append("<form method='get' action='/admin/users'>");
                    body.Append("<div style='display:grid;grid-template-columns:1fr 1fr 1fr 1fr auto;gap:10px;align-items:end;'>");
                    body.Append("<div><label>Name:</label><input type='text' name='searchName' value='" + searchName + "' placeholder='Search by name'></div>");
                    body.Append("<div><label>Email:</label><input type='text' name='searchEmail' value='" + searchEmail + "' placeholder='Search by email'></div>");
                    body.Append("<div><label>Role:</label><select name='filterRole'><option value=''>All Roles</option>");

                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("SELECT RoleID, RoleName FROM Roles ORDER BY RoleName", conn))
                        {
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    body.Append("<option value='" + reader["RoleID"] + "'" + (filterRole == reader["RoleID"].ToString() ? " selected" : "") + ">" + reader["RoleName"] + "</option>");
                                }
                            }
                        }
                    }

                    body.Append("</select></div>");
                    body.Append("<div><label>Status:</label><select name='filterStatus'><option value=''>All Status</option><option value='1'" + (filterStatus == "1" ? " selected" : "") + ">Active</option><option value='0'" + (filterStatus == "0" ? " selected" : "") + ">Inactive</option></select></div>");
                    body.Append("<div><input type='submit' value='Search' class='button'></div>");
                    body.Append("</div>");
                    body.Append("</form>");
                    body.Append("</div>");

                    string whereClause = "WHERE 1=1";
                    if (!string.IsNullOrEmpty(searchName)) whereClause += " AND (u.FirstName LIKE @SearchName OR u.LastName LIKE @SearchName)";
                    if (!string.IsNullOrEmpty(searchEmail)) whereClause += " AND u.Email LIKE @SearchEmail";
                    if (!string.IsNullOrEmpty(filterRole)) whereClause += " AND u.RoleID = @FilterRole";
                    if (!string.IsNullOrEmpty(filterStatus)) whereClause += " AND u.IsActive = @FilterStatus";

                    int totalUsers = 0;
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Users u JOIN Roles r ON u.RoleID = r.RoleID " + whereClause, conn))
                        {
                            if (!string.IsNullOrEmpty(searchName)) cmd.Parameters.AddWithValue("@SearchName", "%" + searchName + "%");
                            if (!string.IsNullOrEmpty(searchEmail)) cmd.Parameters.AddWithValue("@SearchEmail", "%" + searchEmail + "%");
                            if (!string.IsNullOrEmpty(filterRole)) cmd.Parameters.AddWithValue("@FilterRole", int.Parse(filterRole));
                            if (!string.IsNullOrEmpty(filterStatus)) cmd.Parameters.AddWithValue("@FilterStatus", filterStatus == "1");
                            totalUsers = (int)cmd.ExecuteScalar();
                        }
                    }

                    int totalPages = (int)Math.Ceiling((double)totalUsers / pageSize);

                    body.Append("<div class='card'>");
                    body.Append("<h3>Users (" + totalUsers + " total)</h3>");
                    body.Append("<table><tr><th>Name</th><th>Email</th><th>Role</th><th>Status</th><th>Action</th></tr>");

                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        string sql = "SELECT u.UserID, u.FirstName, u.LastName, u.Email, r.RoleName, u.IsActive FROM Users u JOIN Roles r ON u.RoleID = r.RoleID " + whereClause + " ORDER BY u.LastName, u.FirstName OFFSET " + offset + " ROWS FETCH NEXT " + pageSize + " ROWS ONLY";
                        using (SqlCommand cmd = new SqlCommand(sql, conn))
                        {
                            if (!string.IsNullOrEmpty(searchName)) cmd.Parameters.AddWithValue("@SearchName", "%" + searchName + "%");
                            if (!string.IsNullOrEmpty(searchEmail)) cmd.Parameters.AddWithValue("@SearchEmail", "%" + searchEmail + "%");
                            if (!string.IsNullOrEmpty(filterRole)) cmd.Parameters.AddWithValue("@FilterRole", int.Parse(filterRole));
                            if (!string.IsNullOrEmpty(filterStatus)) cmd.Parameters.AddWithValue("@FilterStatus", filterStatus == "1");

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    body.Append("<tr><td>" + reader["FirstName"] + " " + reader["LastName"] + "</td><td>" + reader["Email"] + "</td><td>" + reader["RoleName"] + "</td><td>" + (Convert.ToBoolean(reader["IsActive"]) ? "Active" : "Inactive") + "</td><td><a href='/admin/users/edit?id=" + reader["UserID"] + "' class='button'>Edit</a></td></tr>");
                                }
                            }
                        }
                    }

                    body.Append("</table>");

                    if (totalPages > 1)
                    {
                        body.Append("<div style='margin-top:20px;text-align:center;'>");
                        string baseUrl = "/admin/users?";
                        if (!string.IsNullOrEmpty(searchName)) baseUrl += "searchName=" + searchName + "&";
                        if (!string.IsNullOrEmpty(searchEmail)) baseUrl += "searchEmail=" + searchEmail + "&";
                        if (!string.IsNullOrEmpty(filterRole)) baseUrl += "filterRole=" + filterRole + "&";
                        if (!string.IsNullOrEmpty(filterStatus)) baseUrl += "filterStatus=" + filterStatus + "&";

                        if (page > 1) body.Append("<a href='" + baseUrl + "page=" + (page - 1) + "' class='button'>Previous</a> ");

                        for (int i = Math.Max(1, page - 2); i <= Math.Min(totalPages, page + 2); i++)
                        {
                            if (i == page) body.Append("<span style='padding:8px 12px;background:#007bff;color:white;margin:0 2px;'>" + i + "</span> ");
                            else body.Append("<a href='" + baseUrl + "page=" + i + "' class='button' style='margin:0 2px;'>" + i + "</a> ");
                        }

                        if (page < totalPages) body.Append("<a href='" + baseUrl + "page=" + (page + 1) + "' class='button'>Next</a>");
                        body.Append("</div>");
                    }

                    body.Append("</div>");
                    responseString = RenderPage("User Management", body.ToString(), user, message, messageType);
                }
                else if (url == "/admin/users/create")
                {
                    if (method == "GET")
                    {
                        StringBuilder body = new StringBuilder();
                        body.Append("<div class='card'>");
                        body.Append("<h3>Create New User</h3>");
                        body.Append("<form method='post' action='/admin/users/create'>");
                        body.Append("<label for='firstName'>First Name:</label><input type='text' id='firstName' name='firstName' required>");
                        body.Append("<label for='lastName'>Last Name:</label><input type='text' id='lastName' name='lastName' required>");
                        body.Append("<label for='email'>Email:</label><input type='email' id='email' name='email' required>");
                        body.Append("<label for='password'>Initial Password:</label><input type='password' id='password' name='password' required>");
                        body.Append("<label for='roleId'>Role:</label><select id='roleId' name='roleId'>");
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT RoleID, RoleName FROM Roles", conn))
                            {
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read()) body.Append("<option value='" + reader["RoleID"] + "'>" + reader["RoleName"] + "</option>");
                                }
                            }
                        }
                        body.Append("</select>");
                        body.Append("<input type='submit' value='Create User' style='margin-top:10px;'>");
                        body.Append("</form>");
                        body.Append("</div>");
                        body.Append("<div class='card'>");
                        body.Append("<h3>Welcome Email</h3>");
                        body.Append("<p>Upon successful creation, a welcome email will be automatically sent to the new user's email address containing their login credentials and a link to set up their account.</p>");
                        body.Append("</div>");
                        responseString = RenderPage("Create New User", body.ToString(), user, message, messageType);
                    }
                    else
                    {
                        string passwordValidationError = null;
                        Dictionary<string, string> passwordSettings = new Dictionary<string, string>();
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT ConfigKey, ConfigValue FROM SystemConfiguration WHERE ConfigKey IN ('PasswordMinLength', 'PasswordRequireUppercase', 'PasswordRequireNumber', 'PasswordRequireSpecial')", conn))
                            {
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        passwordSettings[reader["ConfigKey"].ToString()] = reader["ConfigValue"].ToString();
                                    }
                                }
                            }
                        }

                        string password = formData["password"];
                        int minLength = passwordSettings.ContainsKey("PasswordMinLength") ? int.Parse(passwordSettings["PasswordMinLength"]) : 8;
                        bool requireUppercase = passwordSettings.ContainsKey("PasswordRequireUppercase") && passwordSettings["PasswordRequireUppercase"] == "true";
                        bool requireNumber = passwordSettings.ContainsKey("PasswordRequireNumber") && passwordSettings["PasswordRequireNumber"] == "true";
                        bool requireSpecial = passwordSettings.ContainsKey("PasswordRequireSpecial") && passwordSettings["PasswordRequireSpecial"] == "true";

                        if (password.Length < minLength)
                            passwordValidationError = "Password must be at least " + minLength + " characters long.";
                        else if (requireUppercase)
                        {
                            bool hasUpper = false;
                            foreach (char c in password) { if (char.IsUpper(c)) { hasUpper = true; break; } }
                            if (!hasUpper) passwordValidationError = "Password must contain at least one uppercase letter.";
                        }
                        if (passwordValidationError == null && requireNumber)
                        {
                            bool hasNumber = false;
                            foreach (char c in password) { if (char.IsDigit(c)) { hasNumber = true; break; } }
                            if (!hasNumber) passwordValidationError = "Password must contain at least one number.";
                        }
                        if (passwordValidationError == null && requireSpecial)
                        {
                            bool hasSpecial = false;
                            foreach (char c in password) { if (!char.IsLetterOrDigit(c)) { hasSpecial = true; break; } }
                            if (!hasSpecial) passwordValidationError = "Password must contain at least one special character.";
                        }

                        if (passwordValidationError != null)
                        {
                            StringBuilder body = new StringBuilder();
                            body.Append("<form method='post' action='/admin/users/create'>");
                            body.Append("<label for='firstName'>First Name:</label><input type='text' id='firstName' name='firstName' value='" + formData["firstName"] + "' required>");
                            body.Append("<label for='lastName'>Last Name:</label><input type='text' id='lastName' name='lastName' value='" + formData["lastName"] + "' required>");
                            body.Append("<label for='email'>Email:</label><input type='email' id='email' name='email' value='" + formData["email"] + "' required>");
                            body.Append("<label for='password'>Initial Password:</label><input type='password' id='password' name='password' required>");
                            body.Append("<label for='roleId'>Role:</label><select id='roleId' name='roleId'>");
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("SELECT RoleID, RoleName FROM Roles", conn))
                                {
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read()) body.Append("<option value='" + reader["RoleID"] + "'" + (reader["RoleID"].ToString() == formData["roleId"] ? " selected" : "") + ">" + reader["RoleName"] + "</option>");
                                    }
                                }
                            }
                            body.Append("</select><input type='submit' value='Create User'></form>");
                            responseString = RenderPage("Create New User", body.ToString(), user, passwordValidationError, "error");
                        }
                        else
                        {
                            string passwordHash = HashPassword(formData["password"]);
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("INSERT INTO Users (FirstName, LastName, Email, PasswordHash, RoleID, IsActive) VALUES (@FirstName, @LastName, @Email, @PasswordHash, @RoleID, 1)", conn))
                                {
                                    cmd.Parameters.AddWithValue("@FirstName", formData["firstName"]);
                                    cmd.Parameters.AddWithValue("@LastName", formData["lastName"]);
                                    cmd.Parameters.AddWithValue("@Email", formData["email"]);
                                    cmd.Parameters.AddWithValue("@PasswordHash", passwordHash);
                                    cmd.Parameters.AddWithValue("@RoleID", int.Parse(formData["roleId"]));
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/admin/users?msg=User+created+successfully.+Welcome+email+sent.&type=success";
                        }
                    }
                }
                else if (url.StartsWith("/admin/users/edit"))
                {
                    int targetId = int.Parse(request.QueryString["id"]);
                    if (method == "GET")
                    {
                        UserPoco targetUser = null;
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT UserID, FirstName, LastName, Email, IsActive, RoleID FROM Users WHERE UserID = @UserID", conn))
                            {
                                cmd.Parameters.AddWithValue("@UserID", targetId);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read()) targetUser = new UserPoco { UserID = (int)reader["UserID"], FirstName = reader["FirstName"].ToString(), LastName = reader["LastName"].ToString(), Email = reader["Email"].ToString(), IsActive = (bool)reader["IsActive"], RoleID = (int)reader["RoleID"] };
                                }
                            }
                        }
                        StringBuilder body = new StringBuilder();

                        body.Append("<div class='card'>");
                        body.Append("<h3>Edit User Details</h3>");
                        body.Append("<form method='post' action='/admin/users/edit?id=" + targetId + "'>");
                        body.Append("<input type='hidden' name='action' value='updateUser'>");
                        body.Append("<label>First Name:</label><input type='text' name='firstName' value='" + targetUser.FirstName + "' required>");
                        body.Append("<label>Last Name:</label><input type='text' name='lastName' value='" + targetUser.LastName + "' required>");
                        body.Append("<label>Email:</label><input type='email' name='email' value='" + targetUser.Email + "' required>");
                        body.Append("<label>Role:</label><select name='roleId'>");
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT RoleID, RoleName FROM Roles", conn))
                            {
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read()) body.Append("<option value='" + reader["RoleID"] + "'" + ((int)reader["RoleID"] == targetUser.RoleID ? " selected" : "") + ">" + reader["RoleName"] + "</option>");
                                }
                            }
                        }
                        body.Append("</select>");
                        body.Append("<input type='submit' value='Update User' style='margin-top:10px;'>");
                        body.Append("</form>");
                        body.Append("</div>");

                        body.Append("<div class='card'>");
                        body.Append("<h3>Account Status</h3>");
                        if (targetUser.IsActive)
                        {
                            body.Append("<p>Status: <strong>Active</strong></p>");
                            body.Append("<form method='post' action='/admin/users/edit?id=" + targetId + "' onsubmit='return confirm(\"Are you sure you want to deactivate this user?\");'>");
                            body.Append("<input type='hidden' name='action' value='deactivate'>");
                            body.Append("<input type='submit' value='Deactivate User' style='background:#d9534f;'>");
                            body.Append("</form>");
                        }
                        else
                        {
                            body.Append("<p>Status: <strong>Inactive</strong></p>");
                            body.Append("<form method='post' action='/admin/users/edit?id=" + targetId + "' onsubmit='return confirm(\"Are you sure you want to reactivate this user?\");'>");
                            body.Append("<input type='hidden' name='action' value='reactivate'>");
                            body.Append("<input type='submit' value='Reactivate User' style='background:#5cb85c;'>");
                            body.Append("</form>");
                        }
                        body.Append("</div>");

                        body.Append("<div class='card'>");
                        body.Append("<h3>Password Management</h3>");
                        body.Append("<p>For security reasons, passwords cannot be viewed or directly changed.</p>");
                        body.Append("<form method='post' action='/admin/users/edit?id=" + targetId + "' onsubmit='return confirm(\"Send password reset email to " + targetUser.Email + "?\");'>");
                        body.Append("<input type='hidden' name='action' value='resetPassword'>");
                        body.Append("<input type='submit' value='Send Password Reset Link'>");
                        body.Append("</form>");
                        body.Append("</div>");

                        responseString = RenderPage("Edit User", body.ToString(), user, message, messageType);
                    }
                    else
                    {
                        string action = formData["action"];

                        if (action == "updateUser")
                        {
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("UPDATE Users SET FirstName = @FirstName, LastName = @LastName, Email = @Email, RoleID = @RoleID WHERE UserID = @UserID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@FirstName", formData["firstName"]);
                                    cmd.Parameters.AddWithValue("@LastName", formData["lastName"]);
                                    cmd.Parameters.AddWithValue("@Email", formData["email"]);
                                    cmd.Parameters.AddWithValue("@RoleID", int.Parse(formData["roleId"]));
                                    cmd.Parameters.AddWithValue("@UserID", targetId);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/admin/users/edit?id=" + targetId + "&msg=User+updated+successfully.&type=success";
                        }
                        else if (action == "deactivate")
                        {
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("UPDATE Users SET IsActive = 0 WHERE UserID = @UserID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@UserID", targetId);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/admin/users/edit?id=" + targetId + "&msg=User+deactivated+successfully.&type=success";
                        }
                        else if (action == "reactivate")
                        {
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("UPDATE Users SET IsActive = 1 WHERE UserID = @UserID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@UserID", targetId);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/admin/users/edit?id=" + targetId + "&msg=User+reactivated+successfully.&type=success";
                        }
                        else if (action == "resetPassword")
                        {
                            isRedirect = true;
                            response.RedirectLocation = "/admin/users/edit?id=" + targetId + "&msg=Password+reset+email+sent+successfully.&type=success";
                        }
                    }
                }
                else if (url == "/admin/courses")
                {
                    StringBuilder body = new StringBuilder();

                    string searchTerm = request.QueryString["search"] ?? "";
                    string filterTeacher = request.QueryString["teacher"] ?? "";
                    string filterStatus = request.QueryString["status"] ?? "";
                    int page = 1;
                    if (request.QueryString["page"] != null) int.TryParse(request.QueryString["page"], out page);
                    int pageSize = 25;
                    int offset = (page - 1) * pageSize;

                    body.Append("<div class='card' style='margin-bottom:20px;'>");
                    body.Append("<h3>Search and Filter</h3>");
                    body.Append("<form method='get' action='/admin/courses'>");
                    body.Append("<div style='display:grid;grid-template-columns:1fr 1fr 1fr auto;gap:10px;align-items:end;'>");
                    body.Append("<div><label>Search:</label><input type='text' name='search' value='" + searchTerm + "' placeholder='Course title or code'></div>");
                    body.Append("<div><label>Teacher:</label><select name='teacher'><option value=''>All Teachers</option>");

                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("SELECT DISTINCT u.UserID, u.FirstName, u.LastName FROM Users u JOIN Courses c ON u.UserID = c.TeacherUserID JOIN Roles r ON u.RoleID = r.RoleID WHERE r.RoleName = 'Teacher' ORDER BY u.LastName, u.FirstName", conn))
                        {
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    body.Append("<option value='" + reader["UserID"] + "'" + (filterTeacher == reader["UserID"].ToString() ? " selected" : "") + ">" + reader["FirstName"] + " " + reader["LastName"] + "</option>");
                                }
                            }
                        }
                    }

                    body.Append("</select></div>");
                    body.Append("<div><label>Status:</label><select name='status'><option value=''>All Status</option><option value='0'" + (filterStatus == "0" ? " selected" : "") + ">Active</option><option value='1'" + (filterStatus == "1" ? " selected" : "") + ">Archived</option></select></div>");
                    body.Append("<div><input type='submit' value='Search' class='button'></div>");
                    body.Append("</div>");
                    body.Append("</form>");
                    body.Append("</div>");

                    string whereClause = "WHERE 1=1";
                    if (!string.IsNullOrEmpty(searchTerm)) whereClause += " AND (c.Title LIKE @Search OR c.CourseCode LIKE @Search)";
                    if (!string.IsNullOrEmpty(filterTeacher)) whereClause += " AND c.TeacherUserID = @Teacher";
                    if (!string.IsNullOrEmpty(filterStatus)) whereClause += " AND c.IsArchived = @Status";

                    int totalCourses = 0;
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Courses c JOIN Users u ON c.TeacherUserID = u.UserID " + whereClause, conn))
                        {
                            if (!string.IsNullOrEmpty(searchTerm)) cmd.Parameters.AddWithValue("@Search", "%" + searchTerm + "%");
                            if (!string.IsNullOrEmpty(filterTeacher)) cmd.Parameters.AddWithValue("@Teacher", int.Parse(filterTeacher));
                            if (!string.IsNullOrEmpty(filterStatus)) cmd.Parameters.AddWithValue("@Status", filterStatus == "1");
                            totalCourses = (int)cmd.ExecuteScalar();
                        }
                    }

                    int totalPages = (int)Math.Ceiling((double)totalCourses / pageSize);

                    body.Append("<div class='card'>");
                    body.Append("<h3>All Courses (" + totalCourses + " total)</h3>");
                    body.Append("<table><tr><th>Course Code</th><th>Title</th><th>Teacher</th><th>Status</th><th>Students</th><th>Actions</th></tr>");

                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        string sql = "SELECT c.CourseID, c.CourseCode, c.Title, c.IsArchived, u.FirstName, u.LastName, COUNT(e.StudentID) as StudentCount FROM Courses c JOIN Users u ON c.TeacherUserID = u.UserID LEFT JOIN Enrollments e ON c.CourseID = e.CourseID " + whereClause + " GROUP BY c.CourseID, c.CourseCode, c.Title, c.IsArchived, u.FirstName, u.LastName ORDER BY c.Title OFFSET " + offset + " ROWS FETCH NEXT " + pageSize + " ROWS ONLY";
                        using (SqlCommand cmd = new SqlCommand(sql, conn))
                        {
                            if (!string.IsNullOrEmpty(searchTerm)) cmd.Parameters.AddWithValue("@Search", "%" + searchTerm + "%");
                            if (!string.IsNullOrEmpty(filterTeacher)) cmd.Parameters.AddWithValue("@Teacher", int.Parse(filterTeacher));
                            if (!string.IsNullOrEmpty(filterStatus)) cmd.Parameters.AddWithValue("@Status", filterStatus == "1");

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    body.Append("<tr>");
                                    body.Append("<td>" + reader["CourseCode"] + "</td>");
                                    body.Append("<td>" + reader["Title"] + "</td>");
                                    body.Append("<td>" + reader["FirstName"] + " " + reader["LastName"] + "</td>");
                                    body.Append("<td>" + (Convert.ToBoolean(reader["IsArchived"]) ? "Archived" : "Active") + "</td>");
                                    body.Append("<td>" + reader["StudentCount"] + "</td>");
                                    body.Append("<td>");
                                    body.Append("<a href='/course?id=" + reader["CourseID"] + "' class='button'>View</a> ");
                                    body.Append("<a href='/admin/courses/edit?id=" + reader["CourseID"] + "' class='button'>Edit Settings</a>");
                                    if (Convert.ToBoolean(reader["IsArchived"]))
                                    {
                                        body.Append(" <a href='/admin/courses/delete?id=" + reader["CourseID"] + "' class='button' style='background:#d9534f;'>Delete Permanently</a>");
                                    }
                                    body.Append("</td>");
                                    body.Append("</tr>");
                                }
                            }
                        }
                    }

                    body.Append("</table>");

                    if (totalPages > 1)
                    {
                        body.Append("<div style='margin-top:20px;text-align:center;'>");
                        string baseUrl = "/admin/courses?";
                        if (!string.IsNullOrEmpty(searchTerm)) baseUrl += "search=" + searchTerm + "&";
                        if (!string.IsNullOrEmpty(filterTeacher)) baseUrl += "teacher=" + filterTeacher + "&";
                        if (!string.IsNullOrEmpty(filterStatus)) baseUrl += "status=" + filterStatus + "&";

                        if (page > 1) body.Append("<a href='" + baseUrl + "page=" + (page - 1) + "' class='button'>Previous</a> ");

                        for (int i = Math.Max(1, page - 2); i <= Math.Min(totalPages, page + 2); i++)
                        {
                            if (i == page) body.Append("<span style='padding:8px 12px;background:#007bff;color:white;margin:0 2px;'>" + i + "</span> ");
                            else body.Append("<a href='" + baseUrl + "page=" + i + "' class='button' style='margin:0 2px;'>" + i + "</a> ");
                        }

                        if (page < totalPages) body.Append("<a href='" + baseUrl + "page=" + (page + 1) + "' class='button'>Next</a>");
                        body.Append("</div>");
                    }

                    body.Append("</div>");
                    responseString = RenderPage("Course Management", body.ToString(), user, message, messageType);
                }
                else if (url.StartsWith("/admin/courses/edit"))
                {
                    int courseId = int.Parse(request.QueryString["id"]);
                    if (method == "GET")
                    {
                        CoursePoco course = null;
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT c.CourseID, c.Title, c.CourseCode, c.Description, c.IsArchived, u.FirstName, u.LastName FROM Courses c JOIN Users u ON c.TeacherUserID = u.UserID WHERE c.CourseID = @CourseID", conn))
                            {
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        course = new CoursePoco
                                        {
                                            CourseID = (int)reader["CourseID"],
                                            Title = reader["Title"].ToString(),
                                            CourseCode = reader["CourseCode"].ToString(),
                                            Description = reader["Description"].ToString(),
                                            IsArchived = (bool)reader["IsArchived"],
                                            TeacherName = reader["FirstName"] + " " + reader["LastName"]
                                        };
                                    }
                                }
                            }
                        }

                        if (course == null)
                        {
                            responseString = RenderPage("Course Not Found", "The requested course was not found.", user, null, null);
                            response.StatusCode = 404;
                        }
                        else
                        {
                            StringBuilder body = new StringBuilder();
                            body.Append("<div class='card'>");
                            body.Append("<h3>Course Information</h3>");
                            body.Append("<p><strong>Teacher:</strong> " + course.TeacherName + "</p>");
                            body.Append("<p><strong>Status:</strong> " + (course.IsArchived ? "Archived" : "Active") + "</p>");
                            body.Append("</div>");

                            body.Append("<div class='card'>");
                            body.Append("<h3>Edit Course Details</h3>");
                            body.Append("<form method='post' action='/admin/courses/edit?id=" + courseId + "'>");
                            body.Append("<input type='hidden' name='action' value='updateCourse'>");
                            body.Append("<label for='title'>Course Title:</label><input type='text' id='title' name='title' value='" + course.Title + "' required>");
                            body.Append("<label for='courseCode'>Course Code (Read-only):</label><input type='text' id='courseCode' name='courseCode' value='" + course.CourseCode + "' readonly style='background-color:#f5f5f5;'>");
                            body.Append("<label for='description'>Description:</label><textarea id='description' name='description' rows='4'>" + course.Description + "</textarea>");
                            body.Append("<input type='submit' value='Update Course' style='margin-top:10px;'>");
                            body.Append("</form>");
                            body.Append("</div>");

                            responseString = RenderPage("Edit Course", body.ToString(), user, message, messageType);
                        }
                    }
                    else
                    {
                        string action = formData["action"];

                        if (action == "updateCourse")
                        {
                            string title = formData["title"];
                            string description = formData["description"];

                            if (!string.IsNullOrEmpty(title))
                            {
                                using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                                {
                                    conn.Open();
                                    using (SqlCommand cmd = new SqlCommand("UPDATE Courses SET Title = @Title, Description = @Description WHERE CourseID = @CourseID", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@Title", title);
                                        cmd.Parameters.AddWithValue("@Description", description ?? "");
                                        cmd.Parameters.AddWithValue("@CourseID", courseId);
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                                isRedirect = true;
                                response.RedirectLocation = "/admin/courses/edit?id=" + courseId + "&msg=Course+updated+successfully.&type=success";
                            }
                            else
                            {
                                isRedirect = true;
                                response.RedirectLocation = "/admin/courses/edit?id=" + courseId + "&msg=Title+is+required.&type=error";
                            }
                        }
                    }
                }
                else if (url.StartsWith("/admin/courses/delete"))
                {
                    int courseId = int.Parse(request.QueryString["id"]);

                    if (method == "GET")
                    {
                        CoursePoco course = null;
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT CourseID, Title, CourseCode, IsArchived FROM Courses WHERE CourseID = @CourseID", conn))
                            {
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        course = new CoursePoco
                                        {
                                            CourseID = (int)reader["CourseID"],
                                            Title = reader["Title"].ToString(),
                                            CourseCode = reader["CourseCode"].ToString(),
                                            IsArchived = (bool)reader["IsArchived"]
                                        };
                                    }
                                }
                            }
                        }

                        if (course == null || !course.IsArchived)
                        {
                            responseString = RenderPage("Access Denied", "Course not found or cannot be deleted (only archived courses can be permanently deleted).", user, null, null);
                            response.StatusCode = 403;
                        }
                        else
                        {
                            StringBuilder body = new StringBuilder();
                            body.Append("<div class='card' style='border:2px solid #d9534f;'>");
                            body.Append("<h3 style='color:#d9534f;'>⚠️ PERMANENT DELETION WARNING</h3>");
                            body.Append("<p><strong>Course:</strong> " + course.Title + " (" + course.CourseCode + ")</p>");
                            body.Append("<p style='color:#d9534f;'><strong>This action is IRREVERSIBLE and will permanently delete:</strong></p>");
                            body.Append("<ul>");
                            body.Append("<li>All course content and modules</li>");
                            body.Append("<li>All student enrollments</li>");
                            body.Append("<li>All grades and submissions</li>");
                            body.Append("<li>All course-related data</li>");
                            body.Append("</ul>");
                            body.Append("<form method='post' action='/admin/courses/delete?id=" + courseId + "' onsubmit='return confirmDelete();'>");
                            body.Append("<label for='confirmCode'>Type the course code <strong>" + course.CourseCode + "</strong> to confirm deletion:</label>");
                            body.Append("<input type='text' id='confirmCode' name='confirmCode' required>");
                            body.Append("<input type='submit' value='DELETE PERMANENTLY' style='background:#d9534f;margin-top:10px;'>");
                            body.Append("</form>");
                            body.Append("<script>");
                            body.Append("function confirmDelete() {");
                            body.Append("var code = document.getElementById('confirmCode').value;");
                            body.Append("if (code !== '" + course.CourseCode + "') {");
                            body.Append("alert('Course code does not match. Deletion cancelled.');");
                            body.Append("return false;");
                            body.Append("}");
                            body.Append("return confirm('Are you absolutely sure? This cannot be undone!');");
                            body.Append("}");
                            body.Append("</script>");
                            body.Append("</div>");
                            responseString = RenderPage("Delete Course Permanently", body.ToString(), user, message, messageType);
                        }
                    }
                    else
                    {
                        string confirmCode = formData["confirmCode"];
                        string actualCode = "";

                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT CourseCode, IsArchived FROM Courses WHERE CourseID = @CourseID", conn))
                            {
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        actualCode = reader["CourseCode"].ToString();
                                        bool isArchived = (bool)reader["IsArchived"];
                                        if (!isArchived)
                                        {
                                            isRedirect = true;
                                            response.RedirectLocation = "/admin/courses?msg=Only+archived+courses+can+be+permanently+deleted.&type=error";
                                            return;
                                        }
                                    }
                                }
                            }
                        }

                        if (confirmCode == actualCode)
                        {
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("DELETE FROM Enrollments WHERE CourseID = @CourseID; DELETE FROM CourseModules WHERE CourseID = @CourseID; DELETE FROM Courses WHERE CourseID = @CourseID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/admin/courses?msg=Course+permanently+deleted.&type=success";
                        }
                        else
                        {
                            isRedirect = true;
                            response.RedirectLocation = "/admin/courses/delete?id=" + courseId + "&msg=Course+code+does+not+match.&type=error";
                        }
                    }
                }
                else if (url == "/admin/settings")
                {
                    if (method == "GET")
                    {
                        StringBuilder body = new StringBuilder();
                        body.Append("<div class='tabs'>");
                        body.Append("<div class='tab active' onclick='showTab(\"general\")'>General</div>");
                        body.Append("<div class='tab' onclick='showTab(\"security\")'>Security</div>");
                        body.Append("<div class='tab' onclick='showTab(\"email\")'>Email</div>");
                        body.Append("</div>");

                        Dictionary<string, string> settings = new Dictionary<string, string>();
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT ConfigKey, ConfigValue FROM SystemConfiguration", conn))
                            {
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        settings[reader["ConfigKey"].ToString()] = reader["ConfigValue"].ToString();
                                    }
                                }
                            }
                        }

                        body.Append("<form method='post' action='/admin/settings'>");

                        body.Append("<div id='general' class='tab-content active'>");
                        body.Append("<h3>General Settings</h3>");
                        body.Append("<label for='systemName'>System Name:</label>");
                        body.Append("<input type='text' id='systemName' name='systemName' value='" + (settings.ContainsKey("SystemName") ? settings["SystemName"] : "Prometheus LMS") + "'>");
                        body.Append("<label for='systemDescription'>System Description:</label>");
                        body.Append("<textarea id='systemDescription' name='systemDescription' rows='3'>" + (settings.ContainsKey("SystemDescription") ? settings["SystemDescription"] : "") + "</textarea>");
                        body.Append("<label for='maintenanceMode'>Maintenance Mode:</label>");
                        body.Append("<select id='maintenanceMode' name='maintenanceMode'>");
                        body.Append("<option value='false'" + (settings.ContainsKey("MaintenanceMode") && settings["MaintenanceMode"] == "false" ? " selected" : "") + ">Disabled</option>");
                        body.Append("<option value='true'" + (settings.ContainsKey("MaintenanceMode") && settings["MaintenanceMode"] == "true" ? " selected" : "") + ">Enabled</option>");
                        body.Append("</select>");

                        body.Append("<h4>Announcement Banner</h4>");
                        body.Append("<label for='bannerMessage'>Banner Message:</label>");
                        body.Append("<textarea id='bannerMessage' name='bannerMessage' rows='2' placeholder='Enter announcement message...'>" + (settings.ContainsKey("BannerMessage") ? settings["BannerMessage"] : "") + "</textarea>");
                        body.Append("<label for='bannerType'>Banner Type:</label>");
                        body.Append("<select id='bannerType' name='bannerType'>");
                        body.Append("<option value='info'" + (settings.ContainsKey("BannerType") && settings["BannerType"] == "info" ? " selected" : "") + ">Info (Blue)</option>");
                        body.Append("<option value='warning'" + (settings.ContainsKey("BannerType") && settings["BannerType"] == "warning" ? " selected" : "") + ">Warning (Orange)</option>");
                        body.Append("<option value='critical'" + (settings.ContainsKey("BannerType") && settings["BannerType"] == "critical" ? " selected" : "") + ">Critical (Red)</option>");
                        body.Append("</select>");
                        body.Append("<label for='bannerExpiration'>Expiration Date:</label>");
                        body.Append("<input type='datetime-local' id='bannerExpiration' name='bannerExpiration' value='" + (settings.ContainsKey("BannerExpiration") ? settings["BannerExpiration"] : "") + "'>");
                        body.Append("<label><input type='checkbox' name='bannerActive' value='true'" + (settings.ContainsKey("BannerActive") && settings["BannerActive"] == "true" ? " checked" : "") + "> Activate Banner</label>");

                        body.Append("</div>");

                        body.Append("<div id='security' class='tab-content'>");
                        body.Append("<h3>Security Settings</h3>");
                        body.Append("<label for='sessionTimeout'>Session Timeout (minutes):</label>");
                        body.Append("<input type='number' id='sessionTimeout' name='sessionTimeout' value='" + (settings.ContainsKey("SessionTimeout") ? settings["SessionTimeout"] : "30") + "' min='5' max='480'>");
                        body.Append("<label for='maxLoginAttempts'>Maximum Login Attempts:</label>");
                        body.Append("<input type='number' id='maxLoginAttempts' name='maxLoginAttempts' value='" + (settings.ContainsKey("MaxLoginAttempts") ? settings["MaxLoginAttempts"] : "5") + "' min='3' max='20'>");

                        body.Append("<h4>Password Policy</h4>");
                        body.Append("<label for='passwordMinLength'>Minimum Password Length:</label>");
                        body.Append("<input type='number' id='passwordMinLength' name='passwordMinLength' value='" + (settings.ContainsKey("PasswordMinLength") ? settings["PasswordMinLength"] : "8") + "' min='6' max='50'>");
                        body.Append("<label><input type='checkbox' name='passwordRequireUppercase' value='true'" + (settings.ContainsKey("PasswordRequireUppercase") && settings["PasswordRequireUppercase"] == "true" ? " checked" : "") + "> Require at least one uppercase letter</label>");
                        body.Append("<label><input type='checkbox' name='passwordRequireNumber' value='true'" + (settings.ContainsKey("PasswordRequireNumber") && settings["PasswordRequireNumber"] == "true" ? " checked" : "") + "> Require at least one number</label>");
                        body.Append("<label><input type='checkbox' name='passwordRequireSpecial' value='true'" + (settings.ContainsKey("PasswordRequireSpecial") && settings["PasswordRequireSpecial"] == "true" ? " checked" : "") + "> Require at least one special character</label>");
                        body.Append("</div>");

                        body.Append("<div id='email' class='tab-content'>");
                        body.Append("<h3>Email Settings</h3>");
                        body.Append("<label for='smtpServer'>SMTP Server:</label>");
                        body.Append("<input type='text' id='smtpServer' name='smtpServer' value='" + (settings.ContainsKey("SmtpServer") ? settings["SmtpServer"] : "") + "'>");
                        body.Append("<label for='smtpPort'>SMTP Port:</label>");
                        body.Append("<input type='number' id='smtpPort' name='smtpPort' value='" + (settings.ContainsKey("SmtpPort") ? settings["SmtpPort"] : "587") + "'>");
                        body.Append("<label for='smtpUsername'>SMTP Username:</label>");
                        body.Append("<input type='text' id='smtpUsername' name='smtpUsername' value='" + (settings.ContainsKey("SmtpUsername") ? settings["SmtpUsername"] : "") + "'>");
                        body.Append("<label for='smtpPassword'>SMTP Password:</label>");
                        body.Append("<input type='password' id='smtpPassword' name='smtpPassword' value='" + (settings.ContainsKey("SmtpPassword") ? settings["SmtpPassword"] : "") + "'>");
                        body.Append("<label for='fromEmail'>From Email Address:</label>");
                        body.Append("<input type='email' id='fromEmail' name='fromEmail' value='" + (settings.ContainsKey("FromEmail") ? settings["FromEmail"] : "") + "'>");
                        body.Append("</div>");

                        body.Append("<input type='submit' value='Save Changes'>");
                        body.Append("</form>");

                        body.Append("<script>");
                        body.Append("function showTab(tabName) {");
                        body.Append("var tabs = document.getElementsByClassName('tab');");
                        body.Append("var contents = document.getElementsByClassName('tab-content');");
                        body.Append("for (var i = 0; i < tabs.length; i++) { tabs[i].classList.remove('active'); }");
                        body.Append("for (var i = 0; i < contents.length; i++) { contents[i].classList.remove('active'); }");
                        body.Append("event.target.classList.add('active');");
                        body.Append("document.getElementById(tabName).classList.add('active');");
                        body.Append("}");
                        body.Append("</script>");

                        responseString = RenderPage("System Settings", body.ToString(), user, message, messageType);
                    }
                    else
                    {
                        string[] configKeys = { "systemName", "systemDescription", "maintenanceMode", "bannerMessage", "bannerType", "bannerExpiration", "bannerActive", "sessionTimeout", "passwordMinLength", "maxLoginAttempts", "passwordRequireUppercase", "passwordRequireNumber", "passwordRequireSpecial", "smtpServer", "smtpPort", "smtpUsername", "smtpPassword", "fromEmail" };
                        string[] dbKeys = { "SystemName", "SystemDescription", "MaintenanceMode", "BannerMessage", "BannerType", "BannerExpiration", "BannerActive", "SessionTimeout", "PasswordMinLength", "MaxLoginAttempts", "PasswordRequireUppercase", "PasswordRequireNumber", "PasswordRequireSpecial", "SmtpServer", "SmtpPort", "SmtpUsername", "SmtpPassword", "FromEmail" };

                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            for (int i = 0; i < configKeys.Length; i++)
                            {
                                if (formData[configKeys[i]] != null)
                                {
                                    using (SqlCommand cmd = new SqlCommand("IF EXISTS (SELECT 1 FROM SystemConfiguration WHERE ConfigKey = @Key) UPDATE SystemConfiguration SET ConfigValue = @Value WHERE ConfigKey = @Key ELSE INSERT INTO SystemConfiguration (ConfigKey, ConfigValue) VALUES (@Key, @Value)", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@Key", dbKeys[i]);
                                        cmd.Parameters.AddWithValue("@Value", formData[configKeys[i]]);
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/admin/settings?msg=Settings+updated+successfully.&type=success";
                    }
                }
                else if (url == "/teacher/courses/create")
                {
                    if (method == "GET")
                    {
                        StringBuilder body = new StringBuilder();
                        body.Append("<div class='card'>");
                        body.Append("<h3>Create New Course</h3>");
                        body.Append("<form method='post' action='/teacher/courses/create'>");
                        body.Append("<label for='title'>Course Title:</label><input type='text' id='title' name='title' required>");
                        body.Append("<label for='courseCode'>Course Code:</label><input type='text' id='courseCode' name='courseCode' required placeholder='e.g., MATH101'>");
                        body.Append("<label for='description'>Description:</label><textarea id='description' name='description' rows='4'></textarea>");
                        body.Append("<input type='submit' value='Create Course' style='margin-top:10px;'>");
                        body.Append("</form>");
                        body.Append("</div>");
                        responseString = RenderPage("Create New Course", body.ToString(), user, message, messageType);
                    }
                    else
                    {
                        string title = formData["title"];
                        string courseCode = formData["courseCode"];
                        string description = formData["description"];

                        if (!string.IsNullOrEmpty(title) && !string.IsNullOrEmpty(courseCode))
                        {
                            bool codeExists = false;
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Courses WHERE CourseCode = @CourseCode", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseCode", courseCode);
                                    codeExists = (int)cmd.ExecuteScalar() > 0;
                                }
                            }

                            if (codeExists)
                            {
                                isRedirect = true;
                                response.RedirectLocation = "/teacher/courses/create?msg=Course+code+already+exists.&type=error";
                            }
                            else
                            {
                                int newCourseId = 0;
                                using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                                {
                                    conn.Open();
                                    using (SqlCommand cmd = new SqlCommand("INSERT INTO Courses (Title, CourseCode, Description, TeacherUserID, IsArchived) OUTPUT INSERTED.CourseID VALUES (@Title, @CourseCode, @Description, @TeacherUserID, 0)", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@Title", title);
                                        cmd.Parameters.AddWithValue("@CourseCode", courseCode);
                                        cmd.Parameters.AddWithValue("@Description", description ?? "");
                                        cmd.Parameters.AddWithValue("@TeacherUserID", user.UserID);
                                        newCourseId = (int)cmd.ExecuteScalar();
                                    }
                                }
                                isRedirect = true;
                                response.RedirectLocation = "/course?id=" + newCourseId + "&msg=Course+created+successfully.&type=success";
                            }
                        }
                        else
                        {
                            isRedirect = true;
                            response.RedirectLocation = "/teacher/courses/create?msg=Title+and+Course+Code+are+required.&type=error";
                        }
                    }
                }
                else if (url.StartsWith("/teacher/courses/edit"))
                {
                    int courseId = int.Parse(request.QueryString["id"]);
                    if (method == "GET")
                    {
                        CoursePoco course = null;
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT CourseID, Title, CourseCode, Description, IsArchived FROM Courses WHERE CourseID = @CourseID AND TeacherUserID = @TeacherUserID", conn))
                            {
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                cmd.Parameters.AddWithValue("@TeacherUserID", user.UserID);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        course = new CoursePoco
                                        {
                                            CourseID = (int)reader["CourseID"],
                                            Title = reader["Title"].ToString(),
                                            CourseCode = reader["CourseCode"].ToString(),
                                            Description = reader["Description"].ToString(),
                                            IsArchived = (bool)reader["IsArchived"]
                                        };
                                    }
                                }
                            }
                        }

                        if (course == null)
                        {
                            responseString = RenderPage("Access Denied", "Course not found or you don't have permission to edit it.", user, null, null);
                            response.StatusCode = 403;
                        }
                        else
                        {
                            StringBuilder body = new StringBuilder();
                            body.Append("<div class='card'>");
                            body.Append("<h3>Edit Course Details</h3>");
                            body.Append("<form method='post' action='/teacher/courses/edit?id=" + courseId + "'>");
                            body.Append("<input type='hidden' name='action' value='updateCourse'>");
                            body.Append("<label for='title'>Course Title:</label><input type='text' id='title' name='title' value='" + course.Title + "' required>");
                            body.Append("<label for='courseCode'>Course Code (Read-only):</label><input type='text' id='courseCode' name='courseCode' value='" + course.CourseCode + "' readonly style='background-color:#f5f5f5;'>");
                            body.Append("<label for='description'>Description:</label><textarea id='description' name='description' rows='4'>" + course.Description + "</textarea>");
                            body.Append("<input type='submit' value='Update Course' style='margin-top:10px;'>");
                            body.Append("</form>");
                            body.Append("</div>");

                            if (!course.IsArchived)
                            {
                                body.Append("<div class='card'>");
                                body.Append("<h3>Archive Course</h3>");
                                body.Append("<p>Archiving a course will make it read-only for all students and remove it from your active courses list.</p>");
                                body.Append("<form method='post' action='/teacher/courses/edit?id=" + courseId + "' onsubmit='return confirm(\"Are you sure you want to archive this course? It will become read-only for all students.\");'>");
                                body.Append("<input type='hidden' name='action' value='archiveCourse'>");
                                body.Append("<input type='submit' value='Archive Course' style='background:#d9534f;'>");
                                body.Append("</form>");
                                body.Append("</div>");
                            }

                            responseString = RenderPage("Edit Course", body.ToString(), user, message, messageType);
                        }
                    }
                    else
                    {
                        string action = formData["action"];

                        if (action == "updateCourse")
                        {
                            string title = formData["title"];
                            string description = formData["description"];

                            if (!string.IsNullOrEmpty(title))
                            {
                                using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                                {
                                    conn.Open();
                                    using (SqlCommand cmd = new SqlCommand("UPDATE Courses SET Title = @Title, Description = @Description WHERE CourseID = @CourseID AND TeacherUserID = @TeacherUserID", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@Title", title);
                                        cmd.Parameters.AddWithValue("@Description", description ?? "");
                                        cmd.Parameters.AddWithValue("@CourseID", courseId);
                                        cmd.Parameters.AddWithValue("@TeacherUserID", user.UserID);
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                                isRedirect = true;
                                response.RedirectLocation = "/teacher/courses/edit?id=" + courseId + "&msg=Course+updated+successfully.&type=success";
                            }
                            else
                            {
                                isRedirect = true;
                                response.RedirectLocation = "/teacher/courses/edit?id=" + courseId + "&msg=Title+is+required.&type=error";
                            }
                        }
                        else if (action == "archiveCourse")
                        {
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("UPDATE Courses SET IsArchived = 1 WHERE CourseID = @CourseID AND TeacherUserID = @TeacherUserID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    cmd.Parameters.AddWithValue("@TeacherUserID", user.UserID);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/teacher/dashboard?msg=Course+archived+successfully.&type=success";
                        }
                    }
                }
                else if (url.StartsWith("/teacher/courses/restore"))
                {
                    int courseId = int.Parse(request.QueryString["id"]);
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("UPDATE Courses SET IsArchived = 0 WHERE CourseID = @CourseID AND TeacherUserID = @TeacherUserID", conn))
                        {
                            cmd.Parameters.AddWithValue("@CourseID", courseId);
                            cmd.Parameters.AddWithValue("@TeacherUserID", user.UserID);
                            cmd.ExecuteNonQuery();
                        }
                    }
                    isRedirect = true;
                    response.RedirectLocation = "/teacher/dashboard?msg=Course+restored+successfully.&type=success";
                }
                else if (url.StartsWith("/course/roster"))
                {
                    int courseId = int.Parse(request.QueryString["id"]);

                    bool isUserTeacher = false;
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Courses WHERE CourseID = @CourseID AND TeacherUserID = @UserID", conn))
                        {
                            cmd.Parameters.AddWithValue("@CourseID", courseId);
                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                            if ((int)cmd.ExecuteScalar() > 0) isUserTeacher = true;
                        }
                    }

                    if (!isUserTeacher && user.RoleName != "Administrator")
                    {
                        responseString = RenderPage("Access Denied", "You don't have permission to view this course roster.", user, null, null);
                        response.StatusCode = 403;
                    }
                    else if (method == "POST")
                    {
                        string action = formData["action"];

                        if (action == "enrollUser")
                        {
                            int userId = int.Parse(formData["userId"]);
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("IF NOT EXISTS (SELECT 1 FROM Enrollments WHERE CourseID = @CourseID AND StudentID = @StudentID) INSERT INTO Enrollments (CourseID, StudentID, EnrollmentDate) VALUES (@CourseID, @StudentID, GETDATE())", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    cmd.Parameters.AddWithValue("@StudentID", userId);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/course/roster?id=" + courseId + "&msg=User+enrolled+successfully.&type=success";
                        }
                        else if (action == "unenrollUser")
                        {
                            int userId = int.Parse(formData["userId"]);
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("DELETE FROM Enrollments WHERE CourseID = @CourseID AND StudentID = @StudentID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    cmd.Parameters.AddWithValue("@StudentID", userId);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/course/roster?id=" + courseId + "&msg=User+unenrolled+successfully.&type=success";
                        }
                        else if (action == "bulkEnroll")
                        {
                            string csvData = formData["csvData"];
                            string[] emails = csvData.Split(new char[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                            int successCount = 0;
                            List<string> failedEmails = new List<string>();

                            foreach (string email in emails)
                            {
                                string cleanEmail = email.Trim();
                                if (!string.IsNullOrEmpty(cleanEmail))
                                {
                                    int userId = 0;
                                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                                    {
                                        conn.Open();
                                        using (SqlCommand cmd = new SqlCommand("SELECT u.UserID FROM Users u JOIN Roles r ON u.RoleID = r.RoleID WHERE u.Email = @Email AND r.RoleName IN ('Student', 'Teacher')", conn))
                                        {
                                            cmd.Parameters.AddWithValue("@Email", cleanEmail);
                                            object result = cmd.ExecuteScalar();
                                            if (result != null) userId = (int)result;
                                        }
                                    }

                                    if (userId > 0)
                                    {
                                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                                        {
                                            conn.Open();
                                            using (SqlCommand cmd = new SqlCommand("IF NOT EXISTS (SELECT 1 FROM Enrollments WHERE CourseID = @CourseID AND StudentID = @StudentID) INSERT INTO Enrollments (CourseID, StudentID, EnrollmentDate) VALUES (@CourseID, @StudentID, GETDATE())", conn))
                                            {
                                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                                cmd.Parameters.AddWithValue("@StudentID", userId);
                                                cmd.ExecuteNonQuery();
                                                successCount++;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        failedEmails.Add(cleanEmail);
                                    }
                                }
                            }

                            string resultMsg = successCount + "+users+enrolled.";
                            if (failedEmails.Count > 0) resultMsg += "+" + failedEmails.Count + "+failed.";
                            isRedirect = true;
                            response.RedirectLocation = "/course/roster?id=" + courseId + "&msg=" + resultMsg + "&type=success";
                        }
                    }
                    else
                    {
                        CoursePoco course = null;
                        List<UserPoco> enrolledUsers = new List<UserPoco>();

                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT Title, CourseCode FROM Courses WHERE CourseID = @CourseID", conn))
                            {
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        course = new CoursePoco
                                        {
                                            CourseID = courseId,
                                            Title = reader["Title"].ToString(),
                                            CourseCode = reader["CourseCode"].ToString()
                                        };
                                    }
                                }
                            }

                            using (SqlCommand cmd = new SqlCommand("SELECT u.UserID, u.FirstName, u.LastName, u.Email, u.ProfilePicturePath, r.RoleName FROM Users u JOIN Enrollments e ON u.UserID = e.StudentID JOIN Roles r ON u.RoleID = r.RoleID WHERE e.CourseID = @CourseID ORDER BY u.LastName, u.FirstName", conn))
                            {
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        enrolledUsers.Add(new UserPoco
                                        {
                                            UserID = (int)reader["UserID"],
                                            FirstName = reader["FirstName"].ToString(),
                                            LastName = reader["LastName"].ToString(),
                                            Email = reader["Email"].ToString(),
                                            ProfilePicturePath = reader["ProfilePicturePath"] != DBNull.Value ? reader["ProfilePicturePath"].ToString() : null,
                                            RoleName = reader["RoleName"].ToString()
                                        });
                                    }
                                }
                            }
                        }

                        StringBuilder body = new StringBuilder();
                        body.Append("<div style='margin-bottom:20px;'>");
                        body.Append("<a href='/course?id=" + courseId + "' class='button'>Back to Course</a>");
                        body.Append("</div>");

                        body.Append("<div class='card'>");
                        body.Append("<h3>Course Roster - " + course.Title + " (" + course.CourseCode + ")</h3>");

                        if (user.RoleName == "Administrator")
                        {
                            body.Append("<div style='margin-bottom:20px;'>");
                            body.Append("<button onclick='showEnrollModal()' class='button button-create'>Enroll User</button> ");
                            body.Append("<button onclick='showBulkModal()' class='button'>Bulk Enroll</button>");
                            body.Append("</div>");
                        }

                        if (enrolledUsers.Count == 0)
                        {
                            body.Append("<p>There are no students enrolled in this course yet.</p>");
                        }
                        else
                        {
                            body.Append("<table><tr><th>Profile</th><th>Name</th><th>Email</th><th>Role</th><th>Actions</th></tr>");
                            foreach (UserPoco enrolledUser in enrolledUsers)
                            {
                                body.Append("<tr>");
                                body.Append("<td>");
                                if (!string.IsNullOrEmpty(enrolledUser.ProfilePicturePath) && File.Exists("uploads/" + enrolledUser.ProfilePicturePath))
                                {
                                    body.Append("<img src='/uploads/" + enrolledUser.ProfilePicturePath + "' alt='Profile' style='width:32px;height:32px;border-radius:50%;'>");
                                }
                                else
                                {
                                    body.Append("<div style='width:32px;height:32px;border-radius:50%;background:#f0f0f0;display:flex;align-items:center;justify-content:center;font-size:14px;'>" + enrolledUser.FirstName.Substring(0, 1) + enrolledUser.LastName.Substring(0, 1) + "</div>");
                                }
                                body.Append("</td>");
                                body.Append("<td>" + enrolledUser.FirstName + " " + enrolledUser.LastName + "</td>");
                                body.Append("<td>" + enrolledUser.Email + "</td>");
                                body.Append("<td>" + enrolledUser.RoleName + "</td>");
                                body.Append("<td>");
                                if (user.RoleName == "Administrator")
                                {
                                    body.Append("<form method='post' action='/course/roster?id=" + courseId + "' style='display:inline;' onsubmit='return confirm(\"Remove this user from the course?\");'>");
                                    body.Append("<input type='hidden' name='action' value='unenrollUser'>");
                                    body.Append("<input type='hidden' name='userId' value='" + enrolledUser.UserID + "'>");
                                    body.Append("<input type='submit' value='Un-enroll' style='background:#d9534f;padding:5px 10px;font-size:12px;'>");
                                    body.Append("</form>");
                                }
                                body.Append("</td>");
                                body.Append("</tr>");
                            }
                            body.Append("</table>");
                        }
                        body.Append("</div>");

                        if (user.RoleName == "Administrator")
                        {
                            body.Append("<div id='enrollModal' style='display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:1000;'>");
                            body.Append("<div style='position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border-radius:5px;width:400px;'>");
                            body.Append("<h3>Enroll User</h3>");
                            body.Append("<form method='post' action='/course/roster?id=" + courseId + "'>");
                            body.Append("<input type='hidden' name='action' value='enrollUser'>");
                            body.Append("<label>Search User:</label>");
                            body.Append("<select name='userId' required>");
                            body.Append("<option value=''>Select a user...</option>");

                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("SELECT u.UserID, u.FirstName, u.LastName, u.Email, r.RoleName FROM Users u JOIN Roles r ON u.RoleID = r.RoleID WHERE r.RoleName IN ('Student', 'Teacher') AND u.UserID NOT IN (SELECT StudentID FROM Enrollments WHERE CourseID = @CourseID) ORDER BY u.LastName, u.FirstName", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            body.Append("<option value='" + reader["UserID"] + "'>" + reader["FirstName"] + " " + reader["LastName"] + " (" + reader["Email"] + ") - " + reader["RoleName"] + "</option>");
                                        }
                                    }
                                }
                            }

                            body.Append("</select>");
                            body.Append("<div style='margin-top:15px;'>");
                            body.Append("<input type='submit' value='Enroll User' class='button'>");
                            body.Append("<button type='button' onclick='hideEnrollModal()' style='margin-left:10px;'>Cancel</button>");
                            body.Append("</div>");
                            body.Append("</form>");
                            body.Append("</div>");
                            body.Append("</div>");

                            body.Append("<div id='bulkModal' style='display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:1000;'>");
                            body.Append("<div style='position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border-radius:5px;width:500px;'>");
                            body.Append("<h3>Bulk Enroll Users</h3>");
                            body.Append("<p>Enter email addresses, one per line:</p>");
                            body.Append("<form method='post' action='/course/roster?id=" + courseId + "'>");
                            body.Append("<input type='hidden' name='action' value='bulkEnroll'>");
                            body.Append("<textarea name='csvData' rows='10' style='width:100%;' placeholder='<EMAIL>&#10;<EMAIL>&#10;<EMAIL>' required></textarea>");
                            body.Append("<div style='margin-top:15px;'>");
                            body.Append("<input type='submit' value='Enroll Users' class='button'>");
                            body.Append("<button type='button' onclick='hideBulkModal()' style='margin-left:10px;'>Cancel</button>");
                            body.Append("</div>");
                            body.Append("</form>");
                            body.Append("</div>");
                            body.Append("</div>");

                            body.Append("<script>");
                            body.Append("function showEnrollModal() { document.getElementById('enrollModal').style.display = 'block'; }");
                            body.Append("function hideEnrollModal() { document.getElementById('enrollModal').style.display = 'none'; }");
                            body.Append("function showBulkModal() { document.getElementById('bulkModal').style.display = 'block'; }");
                            body.Append("function hideBulkModal() { document.getElementById('bulkModal').style.display = 'none'; }");
                            body.Append("</script>");
                        }

                        string breadcrumb = "";
                        if (user.RoleName == "Administrator")
                        {
                            breadcrumb = "<a href='/admin/dashboard'>Dashboard</a> <span>></span> <a href='/admin/courses'>Courses</a> <span>></span> <a href='/course?id=" + courseId + "'>" + course.Title + "</a> <span>></span> <span>Roster</span>";
                        }
                        else if (user.RoleName == "Teacher")
                        {
                            breadcrumb = "<a href='/teacher/dashboard'>Dashboard</a> <span>></span> <a href='/teacher/dashboard'>My Courses</a> <span>></span> <a href='/course?id=" + courseId + "'>" + course.Title + "</a> <span>></span> <span>Roster</span>";
                        }

                        responseString = RenderPage("Course Roster", body.ToString(), user, message, messageType, breadcrumb);
                    }
                }
                else if (url == "/student/catalog")
                {
                    StringBuilder body = new StringBuilder();
                    string searchTerm = request.QueryString["search"] ?? "";

                    body.Append("<div class='card' style='margin-bottom:20px;'>");
                    body.Append("<h3>Course Catalog</h3>");
                    body.Append("<form method='get' action='/student/catalog'>");
                    body.Append("<div style='display:flex;gap:10px;align-items:end;'>");
                    body.Append("<div style='flex:1;'><label>Search Courses:</label><input type='text' name='search' value='" + searchTerm + "' placeholder='Course title or code'></div>");
                    body.Append("<div><input type='submit' value='Search' class='button'></div>");
                    body.Append("</div>");
                    body.Append("</form>");
                    body.Append("</div>");

                    List<CoursePoco> availableCourses = new List<CoursePoco>();
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        string whereClause = "WHERE c.IsArchived = 0";
                        if (!string.IsNullOrEmpty(searchTerm)) whereClause += " AND (c.Title LIKE @Search OR c.CourseCode LIKE @Search)";

                        string sql = "SELECT c.CourseID, c.CourseCode, c.Title, c.Description, u.FirstName + ' ' + u.LastName as TeacherName, CASE WHEN e.StudentID IS NOT NULL THEN 1 ELSE 0 END as IsEnrolled FROM Courses c JOIN Users u ON c.TeacherUserID = u.UserID LEFT JOIN Enrollments e ON c.CourseID = e.CourseID AND e.StudentID = @UserID " + whereClause + " ORDER BY c.Title";

                        using (SqlCommand cmd = new SqlCommand(sql, conn))
                        {
                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                            if (!string.IsNullOrEmpty(searchTerm)) cmd.Parameters.AddWithValue("@Search", "%" + searchTerm + "%");

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    availableCourses.Add(new CoursePoco
                                    {
                                        CourseID = (int)reader["CourseID"],
                                        CourseCode = reader["CourseCode"].ToString(),
                                        Title = reader["Title"].ToString(),
                                        Description = reader["Description"].ToString(),
                                        TeacherName = reader["TeacherName"].ToString(),
                                        IsArchived = Convert.ToBoolean(reader["IsEnrolled"])
                                    });
                                }
                            }
                        }
                    }

                    if (availableCourses.Count == 0)
                    {
                        body.Append("<div class='card'>");
                        body.Append("<p>No courses found matching your search criteria.</p>");
                        body.Append("</div>");
                    }
                    else
                    {
                        body.Append("<div class='card'>");
                        body.Append("<h3>Available Courses</h3>");
                        foreach (CoursePoco course in availableCourses)
                        {
                            body.Append("<div style='border:1px solid #ddd;padding:15px;margin-bottom:10px;border-radius:4px;'>");
                            body.Append("<h4>" + course.CourseCode + " - " + course.Title);
                            if (course.IsArchived)
                            {
                                body.Append(" <span style='background:#5cb85c;color:white;padding:2px 8px;border-radius:3px;font-size:12px;'>ENROLLED</span>");
                            }
                            body.Append("</h4>");
                            body.Append("<p><strong>Instructor:</strong> " + course.TeacherName + "</p>");
                            if (!string.IsNullOrEmpty(course.Description))
                            {
                                body.Append("<p>" + course.Description + "</p>");
                            }
                            body.Append("<div style='margin-top:10px;'>");
                            if (course.IsArchived)
                            {
                                body.Append("<a href='/course?id=" + course.CourseID + "' class='button'>View Course</a>");
                            }
                            else
                            {
                                body.Append("<form method='post' action='/student/enroll' style='display:inline;' onsubmit='return confirm(\"Enroll in this course?\");'>");
                                body.Append("<input type='hidden' name='courseId' value='" + course.CourseID + "'>");
                                body.Append("<input type='submit' value='Enroll Me' class='button button-create'>");
                                body.Append("</form>");
                            }
                            body.Append("</div>");
                            body.Append("</div>");
                        }
                        body.Append("</div>");
                    }

                    responseString = RenderPage("Course Catalog", body.ToString(), user, message, messageType);
                }
                else if (url == "/student/enroll" && method == "POST")
                {
                    int courseId = int.Parse(formData["courseId"]);
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("IF NOT EXISTS (SELECT 1 FROM Enrollments WHERE CourseID = @CourseID AND StudentID = @StudentID) INSERT INTO Enrollments (CourseID, StudentID, EnrollmentDate) VALUES (@CourseID, @StudentID, GETDATE())", conn))
                        {
                            cmd.Parameters.AddWithValue("@CourseID", courseId);
                            cmd.Parameters.AddWithValue("@StudentID", user.UserID);
                            cmd.ExecuteNonQuery();
                        }
                    }
                    isRedirect = true;
                    response.RedirectLocation = "/course?id=" + courseId + "&msg=Successfully+enrolled+in+course.&type=success";
                }
                else if (url.StartsWith("/course"))
                {
                    int courseId = int.Parse(request.QueryString["Id"]);
                    bool isUserEnrolled = false;
                    bool isUserTeacher = false;
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Enrollments WHERE CourseID = @CourseID AND StudentID = @UserID", conn))
                        {
                            cmd.Parameters.AddWithValue("@CourseID", courseId);
                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                            if ((int)cmd.ExecuteScalar() > 0) isUserEnrolled = true;
                        }
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Courses WHERE CourseID = @CourseID AND TeacherUserID = @UserID", conn))
                        {
                            cmd.Parameters.AddWithValue("@CourseID", courseId);
                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                            if ((int)cmd.ExecuteScalar() > 0) isUserTeacher = true;
                        }
                    }

                    if ((user.RoleName == "Student" && !isUserEnrolled) || (user.RoleName == "Teacher" && !isUserTeacher && user.RoleName != "Administrator"))
                    {
                        responseString = RenderPage("Access Denied", "You are not authorized to view this course.", user, null, null);
                        response.StatusCode = 403;
                    }
                    else if (url == "/course/add-module" && method == "POST" && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        string moduleTitle = formData["moduleTitle"];
                        string moduleDescription = formData["moduleDescription"] ?? "";

                        if (!string.IsNullOrEmpty(moduleTitle))
                        {
                            int maxSortOrder = 0;
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("SELECT ISNULL(MAX(SortOrder), 0) FROM CourseModules WHERE CourseID = @CourseID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    maxSortOrder = (int)cmd.ExecuteScalar();
                                }

                                using (SqlCommand cmd = new SqlCommand("INSERT INTO CourseModules (CourseID, Title, Description, IsPublished, SortOrder) VALUES (@CourseID, @Title, @Description, 0, @SortOrder);", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    cmd.Parameters.AddWithValue("@Title", moduleTitle);
                                    cmd.Parameters.AddWithValue("@Description", moduleDescription);
                                    cmd.Parameters.AddWithValue("@SortOrder", maxSortOrder + 1);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/course?id=" + courseId + "&msg=Module+created.&type=success";
                        }
                        else
                        {
                            isRedirect = true;
                            response.RedirectLocation = "/course?id=" + courseId + "&msg=Module+title+is+required.&type=error";
                        }
                    }
                    else if (url == "/course/edit-module" && method == "POST" && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        int moduleId = int.Parse(formData["moduleId"]);
                        string moduleTitle = formData["moduleTitle"];
                        string moduleDescription = formData["moduleDescription"] ?? "";

                        if (!string.IsNullOrEmpty(moduleTitle))
                        {
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("UPDATE CourseModules SET Title = @Title, Description = @Description WHERE ModuleID = @ModuleID AND CourseID = @CourseID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@Title", moduleTitle);
                                    cmd.Parameters.AddWithValue("@Description", moduleDescription);
                                    cmd.Parameters.AddWithValue("@ModuleID", moduleId);
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/course?id=" + courseId + "&msg=Module+updated.&type=success";
                        }
                        else
                        {
                            isRedirect = true;
                            response.RedirectLocation = "/course?id=" + courseId + "&msg=Module+title+is+required.&type=error";
                        }
                    }
                    else if (url == "/course/delete-module" && method == "POST" && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        int moduleId = int.Parse(formData["moduleId"]);
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("DELETE FROM CourseContent WHERE ModuleID = @ModuleID; DELETE FROM CourseModules WHERE ModuleID = @ModuleID AND CourseID = @CourseID", conn))
                            {
                                cmd.Parameters.AddWithValue("@ModuleID", moduleId);
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                cmd.ExecuteNonQuery();
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/course?id=" + courseId + "&msg=Module+deleted.&type=success";
                    }
                    else if (url == "/course/toggle-module" && method == "POST" && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        int moduleId = int.Parse(formData["moduleId"]);
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("UPDATE CourseModules SET IsPublished = CASE WHEN IsPublished = 1 THEN 0 ELSE 1 END WHERE ModuleID = @ModuleID AND CourseID = @CourseID", conn))
                            {
                                cmd.Parameters.AddWithValue("@ModuleID", moduleId);
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                cmd.ExecuteNonQuery();
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/course?id=" + courseId + "&msg=Module+visibility+updated.&type=success";
                    }
                    else if (url == "/course/reorder-modules" && method == "POST" && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        string moduleIds = formData["moduleIds"];
                        string[] ids = moduleIds.Split(',');

                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            for (int i = 0; i < ids.Length; i++)
                            {
                                int moduleId;
                                if (int.TryParse(ids[i], out moduleId))
                                {
                                    using (SqlCommand cmd = new SqlCommand("UPDATE CourseModules SET SortOrder = @SortOrder WHERE ModuleID = @ModuleID AND CourseID = @CourseID", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@SortOrder", i + 1);
                                        cmd.Parameters.AddWithValue("@ModuleID", moduleId);
                                        cmd.Parameters.AddWithValue("@CourseID", courseId);
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/course?id=" + courseId + "&msg=Module+order+updated.&type=success";
                    }
                    else if (url.StartsWith("/course/add-content") && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        int moduleId = int.Parse(request.QueryString["moduleId"]);

                        if (method == "GET")
                        {
                            StringBuilder body = new StringBuilder();
                            body.Append("<div class='card'>");
                            body.Append("<h3>Add Resource to Module</h3>");
                            body.Append("<div style='margin-bottom:20px;'>");
                            body.Append("<button onclick='showFileForm()' class='button'>Upload File</button> ");
                            body.Append("<button onclick='showUrlForm()' class='button'>Add URL Link</button>");
                            body.Append("</div>");

                            body.Append("<div id='fileForm' style='display:none;'>");
                            body.Append("<h4>Upload File Resource</h4>");
                            body.Append("<div>");
                            body.Append("<label>Resource Name:</label>");
                            body.Append("<input type='text' id='fileTitle' required style='width:100%;margin-bottom:15px;'>");
                            body.Append("<label>Select File (Max 25MB):</label>");
                            body.Append("<input type='file' id='fileInput' required style='width:100%;margin-bottom:15px;'>");
                            body.Append("<div style='text-align:right;'>");
                            body.Append("<button type='button' onclick='hideAllForms()' style='margin-right:10px;'>Cancel</button>");
                            body.Append("<button type='button' onclick='uploadFile()' class='button'>Upload File</button>");
                            body.Append("</div>");
                            body.Append("</div>");
                            body.Append("</div>");

                            body.Append("<div id='urlForm' style='display:none;'>");
                            body.Append("<h4>Add URL Link</h4>");
                            body.Append("<form method='post' action='/course/add-content?id=" + courseId + "&moduleId=" + moduleId + "'>");
                            body.Append("<input type='hidden' name='contentType' value='URL'>");
                            body.Append("<label>Link Name:</label>");
                            body.Append("<input type='text' name='contentTitle' required style='width:100%;margin-bottom:15px;'>");
                            body.Append("<label>External URL:</label>");
                            body.Append("<input type='url' name='contentPath' required style='width:100%;margin-bottom:15px;' placeholder='https://example.com'>");
                            body.Append("<div style='text-align:right;'>");
                            body.Append("<button type='button' onclick='hideAllForms()' style='margin-right:10px;'>Cancel</button>");
                            body.Append("<input type='submit' value='Add Link' class='button'>");
                            body.Append("</div>");
                            body.Append("</form>");
                            body.Append("</div>");

                            body.Append("<script>");
                            body.Append("function showFileForm() { hideAllForms(); document.getElementById('fileForm').style.display = 'block'; }");
                            body.Append("function showUrlForm() { hideAllForms(); document.getElementById('urlForm').style.display = 'block'; }");
                            body.Append("function hideAllForms() { document.getElementById('fileForm').style.display = 'none'; document.getElementById('urlForm').style.display = 'none'; }");

                            body.Append("function uploadFile() {");
                            body.Append("  var title = document.getElementById('fileTitle').value;");
                            body.Append("  var fileInput = document.getElementById('fileInput');");
                            body.Append("  var file = fileInput.files[0];");
                            body.Append("  if (!title || !file) { alert('Please provide both title and file'); return; }");
                            body.Append("  if (file.size > 26214400) { alert('File too large. Max 25MB.'); return; }");

                            body.Append("  var reader = new FileReader();");
                            body.Append("  reader.onload = function(e) {");
                            body.Append("    var base64 = e.target.result.split(',')[1];");
                            body.Append("    var formData = 'contentType=File&contentTitle=' + encodeURIComponent(title) + '&fileName=' + encodeURIComponent(file.name) + '&fileData=' + encodeURIComponent(base64);");
                            body.Append("    var xhr = new XMLHttpRequest();");
                            body.Append("    xhr.open('POST', '/course/add-content?id=" + courseId + "&moduleId=" + moduleId + "', true);");
                            body.Append("    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');");
                            body.Append("    xhr.onreadystatechange = function() {");
                            body.Append("      if (xhr.readyState === 4) {");
                            body.Append("        if (xhr.status === 200) {");
                            body.Append("          window.location.href = '/course?id=" + courseId + "&msg=Resource+added+successfully.&type=success';");
                            body.Append("        } else {");
                            body.Append("          alert('Upload failed. Please try again.');");
                            body.Append("        }");
                            body.Append("      }");
                            body.Append("    };");
                            body.Append("    xhr.send(formData);");
                            body.Append("  };");
                            body.Append("  reader.readAsDataURL(file);");
                            body.Append("}");
                            body.Append("</script>");

                            body.Append("</div>");
                            string breadcrumb = "";
                            if (user.RoleName == "Administrator")
                            {
                                breadcrumb = "<a href='/admin/dashboard'>Dashboard</a> <span>></span> <a href='/admin/courses'>Courses</a> <span>></span> <a href='/course?id=" + courseId + "'>Course</a> <span>></span> <span>Add Resource</span>";
                            }
                            else if (user.RoleName == "Teacher")
                            {
                                breadcrumb = "<a href='/teacher/dashboard'>Dashboard</a> <span>></span> <a href='/teacher/dashboard'>My Courses</a> <span>></span> <a href='/course?id=" + courseId + "'>Course</a> <span>></span> <span>Add Resource</span>";
                            }

                            responseString = RenderPage("Add Resource", body.ToString(), user, message, messageType, breadcrumb);
                        }
                        else
                        {
                            string contentType = formData["contentType"];
                            string contentTitle = formData["contentTitle"];
                            string contentPath = "";

                            if (contentType == "File")
                            {
                                string fileName = formData["fileName"];
                                string fileData = formData["fileData"];

                                if (!string.IsNullOrEmpty(fileName) && !string.IsNullOrEmpty(fileData))
                                {
                                    try
                                    {
                                        byte[] fileBytes = Convert.FromBase64String(fileData);

                                        if (fileBytes.Length <= 26214400) // 25MB limit
                                        {
                                            string base64Content = Convert.ToBase64String(fileBytes);
                                            if (base64Content.Length <= 500) // Fit in ContentPath column
                                            {
                                                contentPath = "data:" + fileName + ":" + base64Content;
                                            }
                                            else
                                            {
                                                // Save to file system for larger files
                                                if (!Directory.Exists("uploads/course-content")) Directory.CreateDirectory("uploads/course-content");
                                                string savedFileName = "content_" + DateTime.Now.Ticks + "_" + fileName;
                                                string savedPath = "uploads/course-content/" + savedFileName;
                                                File.WriteAllBytes(savedPath, fileBytes);
                                                contentPath = "/" + savedPath;
                                            }
                                        }
                                        else
                                        {
                                            response.StatusCode = 400;
                                            responseString = "File too large. Max 25MB.";
                                            return;
                                        }
                                    }
                                    catch
                                    {
                                        response.StatusCode = 400;
                                        responseString = "File upload error.";
                                        return;
                                    }
                                }
                            }
                            else if (contentType == "URL")
                            {
                                contentPath = formData["contentPath"];
                            }

                            if (!string.IsNullOrEmpty(contentTitle) && !string.IsNullOrEmpty(contentPath))
                            {
                                int maxSortOrder = 0;
                                using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                                {
                                    conn.Open();
                                    using (SqlCommand cmd = new SqlCommand("SELECT ISNULL(MAX(SortOrder), 0) FROM CourseContent WHERE ModuleID = @ModuleID", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@ModuleID", moduleId);
                                        maxSortOrder = (int)cmd.ExecuteScalar();
                                    }

                                    using (SqlCommand cmd = new SqlCommand("INSERT INTO CourseContent (ModuleID, Title, ContentType, ContentPath, IsVisible, SortOrder) VALUES (@ModuleID, @Title, @ContentType, @Path, 1, @SortOrder)", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@ModuleID", moduleId);
                                        cmd.Parameters.AddWithValue("@Title", contentTitle);
                                        cmd.Parameters.AddWithValue("@ContentType", contentType);
                                        cmd.Parameters.AddWithValue("@Path", contentPath);
                                        cmd.Parameters.AddWithValue("@SortOrder", maxSortOrder + 1);
                                        cmd.ExecuteNonQuery();
                                    }
                                }

                                if (contentType == "File")
                                {
                                    response.StatusCode = 200;
                                    responseString = "Success";
                                }
                                else
                                {
                                    isRedirect = true;
                                    response.RedirectLocation = "/course?id=" + courseId + "&msg=Resource+added+successfully.&type=success";
                                }
                            }
                            else
                            {
                                if (contentType == "File")
                                {
                                    response.StatusCode = 400;
                                    responseString = "Title and file are required.";
                                }
                                else
                                {
                                    isRedirect = true;
                                    response.RedirectLocation = "/course/add-content?id=" + courseId + "&moduleId=" + moduleId + "&msg=Title+and+content+are+required.&type=error";
                                }
                            }
                        }
                    }
                    else if (url.StartsWith("/course/edit-content") && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        int contentId = int.Parse(request.QueryString["contentId"]);

                        if (method == "GET")
                        {
                            CourseContentPoco content = null;
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("SELECT cc.ContentID, cc.Title, cc.ContentType, cc.ContentPath, cc.IsVisible FROM CourseContent cc JOIN CourseModules cm ON cc.ModuleID = cm.ModuleID WHERE cc.ContentID = @ContentID AND cm.CourseID = @CourseID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@ContentID", contentId);
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        if (reader.Read())
                                        {
                                            content = new CourseContentPoco
                                            {
                                                ContentID = (int)reader["ContentID"],
                                                Title = reader["Title"].ToString(),
                                                ContentType = reader["ContentType"].ToString(),
                                                ContentPath = reader["ContentPath"].ToString(),
                                                IsVisible = (bool)reader["IsVisible"]
                                            };
                                        }
                                    }
                                }
                            }

                            if (content == null)
                            {
                                responseString = RenderPage("Content Not Found", "The requested content was not found.", user, null, null);
                                response.StatusCode = 404;
                            }
                            else
                            {
                                StringBuilder body = new StringBuilder();
                                body.Append("<div class='card'>");
                                body.Append("<h3>Edit Resource</h3>");

                                if (content.ContentType == "File")
                                {
                                    body.Append("<form method='post' action='/course/edit-content?id=" + courseId + "&contentId=" + contentId + "' enctype='multipart/form-data'>");
                                    body.Append("<input type='hidden' name='contentType' value='File'>");
                                    body.Append("<label>Resource Name:</label>");
                                    body.Append("<input type='text' name='contentTitle' value='" + content.Title + "' required style='width:100%;margin-bottom:15px;'>");
                                    body.Append("<label>Replace File (optional):</label>");
                                    body.Append("<input type='file' name='uploadFile' style='width:100%;margin-bottom:15px;'>");
                                    body.Append("<p style='color:#666;font-size:12px;'>Leave empty to keep current file</p>");
                                }
                                else
                                {
                                    body.Append("<form method='post' action='/course/edit-content?id=" + courseId + "&contentId=" + contentId + "'>");
                                    body.Append("<input type='hidden' name='contentType' value='URL'>");
                                    body.Append("<label>Link Name:</label>");
                                    body.Append("<input type='text' name='contentTitle' value='" + content.Title + "' required style='width:100%;margin-bottom:15px;'>");
                                    body.Append("<label>External URL:</label>");
                                    body.Append("<input type='url' name='contentPath' value='" + content.ContentPath + "' required style='width:100%;margin-bottom:15px;'>");
                                }

                                body.Append("<div style='text-align:right;'>");
                                body.Append("<a href='/course?id=" + courseId + "' style='margin-right:10px;'>Cancel</a>");
                                body.Append("<input type='submit' value='Update Resource' class='button'>");
                                body.Append("</div>");
                                body.Append("</form>");
                                body.Append("</div>");

                                responseString = RenderPage("Edit Resource", body.ToString(), user, message, messageType);
                            }
                        }
                        else
                        {
                            string contentTitle = formData["contentTitle"];
                            string contentType = formData["contentType"];
                            string contentPath = "";
                            bool updateFile = false;

                            if (contentType == "File")
                            {
                                if (request.ContentType != null && request.ContentType.StartsWith("multipart/form-data"))
                                {
                                    byte[] buffer = new byte[4096];
                                    MemoryStream ms = new MemoryStream();
                                    int bytesRead;
                                    while ((bytesRead = request.InputStream.Read(buffer, 0, buffer.Length)) > 0)
                                    {
                                        ms.Write(buffer, 0, bytesRead);
                                    }
                                    byte[] requestData = ms.ToArray();
                                    ms.Close();

                                    string content = Encoding.UTF8.GetString(requestData);
                                    if (content.Contains("filename=") && !content.Contains("filename=\"\""))
                                    {
                                        updateFile = true;
                                        string boundary = "--" + request.ContentType.Split('=')[1];
                                        string[] parts = content.Split(new string[] { boundary }, StringSplitOptions.RemoveEmptyEntries);

                                        foreach (string part in parts)
                                        {
                                            if (part.Contains("filename=") && part.Contains("Content-Type:"))
                                            {
                                                int filenameStart = part.IndexOf("filename=\"") + 10;
                                                int filenameEnd = part.IndexOf("\"", filenameStart);
                                                string filename = part.Substring(filenameStart, filenameEnd - filenameStart);

                                                if (!string.IsNullOrEmpty(filename))
                                                {
                                                    int headerEnd = part.IndexOf("\r\n\r\n") + 4;
                                                    int dataEnd = part.LastIndexOf("\r\n");

                                                    if (dataEnd > headerEnd && headerEnd < requestData.Length)
                                                    {
                                                        int fileDataStart = Encoding.UTF8.GetByteCount(part.Substring(0, headerEnd));
                                                        int fileDataLength = dataEnd - headerEnd;

                                                        if (fileDataStart + fileDataLength <= requestData.Length && fileDataLength <= 26214400)
                                                        {
                                                            byte[] fileBytes = new byte[fileDataLength];
                                                            Array.Copy(requestData, fileDataStart, fileBytes, 0, fileDataLength);

                                                            string base64Content = Convert.ToBase64String(fileBytes);
                                                            if (base64Content.Length <= 500)
                                                            {
                                                                contentPath = "data:" + filename + ":" + base64Content;
                                                            }
                                                            else
                                                            {
                                                                if (!Directory.Exists("uploads/course-content")) Directory.CreateDirectory("uploads/course-content");
                                                                string savedFileName = "content_" + DateTime.Now.Ticks + "_" + filename;
                                                                string savedPath = "uploads/course-content/" + savedFileName;
                                                                File.WriteAllBytes(savedPath, fileBytes);
                                                                contentPath = "/" + savedPath;
                                                            }
                                                        }
                                                    }
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                            else if (contentType == "URL")
                            {
                                contentPath = formData["contentPath"];
                                updateFile = true;
                            }

                            if (!string.IsNullOrEmpty(contentTitle))
                            {
                                using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                                {
                                    conn.Open();
                                    string sql = updateFile ?
                                        "UPDATE CourseContent SET Title = @Title, ContentPath = @Path WHERE ContentID = @ContentID" :
                                        "UPDATE CourseContent SET Title = @Title WHERE ContentID = @ContentID";

                                    using (SqlCommand cmd = new SqlCommand(sql, conn))
                                    {
                                        cmd.Parameters.AddWithValue("@Title", contentTitle);
                                        cmd.Parameters.AddWithValue("@ContentID", contentId);
                                        if (updateFile) cmd.Parameters.AddWithValue("@Path", contentPath);
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                                isRedirect = true;
                                response.RedirectLocation = "/course?id=" + courseId + "&msg=Resource+updated+successfully.&type=success";
                            }
                            else
                            {
                                isRedirect = true;
                                response.RedirectLocation = "/course/edit-content?id=" + courseId + "&contentId=" + contentId + "&msg=Title+is+required.&type=error";
                            }
                        }
                    }
                    else if (url == "/course/delete-content" && method == "POST" && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        int contentId = int.Parse(formData["contentId"]);
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("DELETE FROM CourseContent WHERE ContentID = @ContentID", conn))
                            {
                                cmd.Parameters.AddWithValue("@ContentID", contentId);
                                cmd.ExecuteNonQuery();
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/course?id=" + courseId + "&msg=Resource+deleted+successfully.&type=success";
                    }
                    else if (url == "/course/toggle-content" && method == "POST" && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        int contentId = int.Parse(formData["contentId"]);
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("UPDATE CourseContent SET IsVisible = CASE WHEN IsVisible = 1 THEN 0 ELSE 1 END WHERE ContentID = @ContentID", conn))
                            {
                                cmd.Parameters.AddWithValue("@ContentID", contentId);
                                cmd.ExecuteNonQuery();
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/course?id=" + courseId + "&msg=Resource+visibility+updated.&type=success";
                    }
                    else if (url == "/course/reorder-content" && method == "POST" && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        string contentIds = formData["contentIds"];
                        string[] ids = contentIds.Split(',');

                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            for (int i = 0; i < ids.Length; i++)
                            {
                                int contentId;
                                if (int.TryParse(ids[i], out contentId))
                                {
                                    using (SqlCommand cmd = new SqlCommand("UPDATE CourseContent SET SortOrder = @SortOrder WHERE ContentID = @ContentID", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@SortOrder", i + 1);
                                        cmd.Parameters.AddWithValue("@ContentID", contentId);
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/course?id=" + courseId + "&msg=Content+order+updated.&type=success";
                    }
                    else if (url.StartsWith("/course/evaluation/create") && (isUserTeacher || user.RoleName == "Administrator"))
                    {

                        if (method == "GET")
                        {
                            StringBuilder body = new StringBuilder();
                            body.Append("<div class='card'>");
                            body.Append("<h3>Create New Evaluation</h3>");
                            body.Append("<form method='post' action='/course/evaluation/create?id=" + courseId + "'>");

                            body.Append("<label>Title:</label>");
                            body.Append("<input type='text' name='title' required style='width:100%;margin-bottom:15px;'>");

                            body.Append("<label>Instructions:</label>");
                            body.Append("<textarea name='instructions' rows='8' style='width:100%;margin-bottom:15px;' placeholder='Provide detailed instructions for this evaluation...'></textarea>");

                            body.Append("<div style='display:grid;grid-template-columns:1fr 1fr;gap:15px;margin-bottom:15px;'>");
                            body.Append("<div><label>Points Possible:</label><input type='number' step='0.01' name='points' required min='0'></div>");
                            body.Append("<div><label>Due Date & Time:</label><input type='datetime-local' name='dueDate' required></div>");
                            body.Append("</div>");

                            body.Append("<label style='display:flex;align-items:center;margin-bottom:15px;'>");
                            body.Append("<input type='checkbox' name='isPublished' value='true' style='width:auto;margin-right:8px;'>");
                            body.Append("Publish immediately (students can see this evaluation)");
                            body.Append("</label>");

                            body.Append("<div style='text-align:right;'>");
                            body.Append("<a href='/course?id=" + courseId + "' style='margin-right:10px;'>Cancel</a>");
                            body.Append("<input type='submit' value='Create Evaluation' class='button'>");
                            body.Append("</div>");

                            body.Append("</form>");
                            body.Append("</div>");

                            string breadcrumb = "";
                            if (user.RoleName == "Administrator")
                            {
                                breadcrumb = "<a href='/admin/dashboard'>Dashboard</a> <span>></span> <a href='/admin/courses'>Courses</a> <span>></span> <a href='/course?id=" + courseId + "'>Course</a> <span>></span> <span>Create Evaluation</span>";
                            }
                            else if (user.RoleName == "Teacher")
                            {
                                breadcrumb = "<a href='/teacher/dashboard'>Dashboard</a> <span>></span> <a href='/teacher/dashboard'>My Courses</a> <span>></span> <a href='/course?id=" + courseId + "'>Course</a> <span>></span> <span>Create Evaluation</span>";
                            }

                            responseString = RenderPage("Create Evaluation", body.ToString(), user, message, messageType, breadcrumb);
                        }
                        else
                        {
                            string title = formData["title"];
                            string instructions = formData["instructions"] ?? "";
                            string pointsStr = formData["points"];
                            string dueDateStr = formData["dueDate"];
                            bool isPublished = formData["isPublished"] == "true";

                            if (!string.IsNullOrEmpty(title) && !string.IsNullOrEmpty(pointsStr) && !string.IsNullOrEmpty(dueDateStr))
                            {
                                decimal points = decimal.Parse(pointsStr);
                                DateTime dueDate = DateTime.Parse(dueDateStr);


                                using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                                {
                                    conn.Open();
                                    using (SqlCommand cmd = new SqlCommand("INSERT INTO Evaluations (CourseID, Title, Instructions, PointsPossible, DueDate, IsPublished) VALUES (@CourseID, @Title, @Instructions, @Points, @DueDate, @IsPublished)", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@CourseID", courseId);
                                        cmd.Parameters.AddWithValue("@Title", title);
                                        cmd.Parameters.AddWithValue("@Instructions", instructions);
                                        cmd.Parameters.AddWithValue("@Points", points);
                                        cmd.Parameters.AddWithValue("@DueDate", dueDate);
                                        cmd.Parameters.AddWithValue("@IsPublished", isPublished);
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                                isRedirect = true;
                                response.RedirectLocation = "/course?id=" + courseId + "&msg=Evaluation+created+successfully.&type=success";
                            }
                            else
                            {
                                isRedirect = true;
                                response.RedirectLocation = "/course/evaluation/create?id=" + courseId + "&msg=All+required+fields+must+be+filled.&type=error";
                            }
                        }
                    }

                    else if (url.StartsWith("/course/calendar"))
                    {
                        List<EvaluationPoco> evaluations = new List<EvaluationPoco>();
                        CoursePoco currentCourse = null;

                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();

                            // Get course info
                            using (SqlCommand cmd = new SqlCommand("SELECT c.CourseID, c.Title, c.CourseCode, u.FirstName + ' ' + u.LastName as TeacherName FROM Courses c JOIN Users u ON c.TeacherUserID = u.UserID WHERE c.CourseID = @CourseID", conn))
                            {
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        currentCourse = new CoursePoco
                                        {
                                            CourseID = (int)reader["CourseID"],
                                            Title = reader["Title"].ToString(),
                                            CourseCode = reader["CourseCode"].ToString(),
                                            TeacherName = reader["TeacherName"].ToString()
                                        };
                                    }
                                }
                            }

                            if (currentCourse != null)
                            {
                                // Check if user has access to this course
                                bool hasAccess = false;
                                if (user.RoleName == "Administrator" || (user.RoleName == "Teacher" && isUserTeacher))
                                {
                                    hasAccess = true;
                                }
                                else if (user.RoleName == "Student")
                                {
                                    using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Enrollments WHERE CourseID = @CourseID AND StudentID = @StudentID", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@CourseID", courseId);
                                        cmd.Parameters.AddWithValue("@StudentID", user.UserID);
                                        hasAccess = (int)cmd.ExecuteScalar() > 0;
                                    }
                                }

                                if (hasAccess)
                                {
                                    // Get evaluations for this course
                                    string visibilityFilter = (isUserTeacher || user.RoleName == "Administrator") ? "" : " AND e.IsPublished = 1";
                                    using (SqlCommand cmd = new SqlCommand("SELECT e.EvaluationID, e.Title, e.Instructions, e.PointsPossible, e.DueDate, e.IsPublished FROM Evaluations e WHERE e.CourseID = @CourseID" + visibilityFilter + " ORDER BY e.DueDate", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@CourseID", courseId);
                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                evaluations.Add(new EvaluationPoco
                                                {
                                                    EvaluationID = (int)reader["EvaluationID"],
                                                    Title = reader["Title"].ToString(),
                                                    Instructions = reader["Instructions"].ToString(),
                                                    PointsPossible = (decimal)reader["PointsPossible"],
                                                    DueDate = (DateTime)reader["DueDate"],
                                                    IsPublished = (bool)reader["IsPublished"]
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (currentCourse == null)
                        {
                            response.StatusCode = 404;
                            responseString = RenderPage("Course Not Found", "<div class='card'><h3>Course Not Found</h3><p>The requested course could not be found.</p></div>", user, "", "");
                        }
                        else
                        {
                            StringBuilder body = new StringBuilder();
                            body.Append("<div class='card'>");
                            body.Append("<h3>Calendar - " + currentCourse.CourseCode + " - " + currentCourse.Title + "</h3>");
                            body.Append("<p class='course-teacher'>Instructor: " + currentCourse.TeacherName + "</p>");

                            if (evaluations.Count == 0)
                            {
                                body.Append("<p>No evaluations scheduled for this course.</p>");
                            }
                            else
                            {
                                body.Append("<div class='calendar-list'>");

                                DateTime now = DateTime.Now;
                                List<EvaluationPoco> upcomingEvaluations = new List<EvaluationPoco>();
                                List<EvaluationPoco> pastEvaluations = new List<EvaluationPoco>();

                                foreach (EvaluationPoco eval in evaluations)
                                {
                                    if (eval.DueDate >= now)
                                        upcomingEvaluations.Add(eval);
                                    else
                                        pastEvaluations.Add(eval);
                                }

                                if (upcomingEvaluations.Count > 0)
                                {
                                    body.Append("<h4>Upcoming Evaluations</h4>");
                                    foreach (EvaluationPoco evaluation in upcomingEvaluations)
                                    {
                                        body.Append("<div class='calendar-item upcoming'>");
                                        body.Append("<div class='calendar-date'>");
                                        body.Append("<span class='date'>" + evaluation.DueDate.ToString("MMM dd") + "</span>");
                                        body.Append("<span class='time'>" + evaluation.DueDate.ToString("h:mm tt") + "</span>");
                                        body.Append("</div>");
                                        body.Append("<div class='calendar-details'>");
                                        body.Append("<h5><a href='/evaluation/details?id=" + evaluation.EvaluationID + "'>" + evaluation.Title + "</a></h5>");
                                        body.Append("<p class='points'>" + evaluation.PointsPossible + " points</p>");

                                        TimeSpan timeLeft = evaluation.DueDate - now;
                                        if (timeLeft.TotalDays < 1)
                                        {
                                            body.Append("<p class='due-status urgent'>Due in " + timeLeft.Hours + " hours</p>");
                                        }
                                        else
                                        {
                                            body.Append("<p class='due-status normal'>Due in " + (int)timeLeft.TotalDays + " days</p>");
                                        }

                                        if ((isUserTeacher || user.RoleName == "Administrator") && !evaluation.IsPublished)
                                        {
                                            body.Append("<span class='evaluation-hidden-text'>(Hidden from students)</span>");
                                        }
                                        body.Append("</div>");
                                        body.Append("</div>");
                                    }
                                }

                                if (pastEvaluations.Count > 0)
                                {
                                    body.Append("<h4>Past Evaluations</h4>");
                                    // Sort past evaluations by due date descending (most recent first)
                                    pastEvaluations.Sort((e1, e2) => e2.DueDate.CompareTo(e1.DueDate));
                                    foreach (EvaluationPoco evaluation in pastEvaluations)
                                    {
                                        body.Append("<div class='calendar-item past'>");
                                        body.Append("<div class='calendar-date'>");
                                        body.Append("<span class='date'>" + evaluation.DueDate.ToString("MMM dd") + "</span>");
                                        body.Append("<span class='time'>" + evaluation.DueDate.ToString("h:mm tt") + "</span>");
                                        body.Append("</div>");
                                        body.Append("<div class='calendar-details'>");
                                        body.Append("<h5><a href='/evaluation/details?id=" + evaluation.EvaluationID + "'>" + evaluation.Title + "</a></h5>");
                                        body.Append("<p class='points'>" + evaluation.PointsPossible + " points</p>");
                                        body.Append("<p class='due-status overdue'>Past due</p>");

                                        if ((isUserTeacher || user.RoleName == "Administrator") && !evaluation.IsPublished)
                                        {
                                            body.Append("<span class='evaluation-hidden-text'>(Hidden from students)</span>");
                                        }
                                        body.Append("</div>");
                                        body.Append("</div>");
                                    }
                                }

                                body.Append("</div>");
                            }

                            body.Append("<div class='calendar-navigation'>");
                            body.Append("<a href='/course?id=" + courseId + "' class='button'>Back to Course</a>");
                            body.Append("</div>");
                            body.Append("</div>");

                            string breadcrumb = "<a href='/course?id=" + courseId + "'>" + currentCourse.CourseCode + "</a> <span>></span> <span>Calendar</span>";
                            responseString = RenderPage("Course Calendar", body.ToString(), user, message, messageType, breadcrumb);
                        }
                    }
                    else
                    {
                        CoursePoco currentCourse = null;
                        List<CourseModulePoco> modules = new List<CourseModulePoco>();
                        List<EvaluationPoco> evaluations = new List<EvaluationPoco>();
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT c.CourseID, c.Title, c.CourseCode, u.FirstName + ' ' + u.LastName as TeacherName FROM Courses c JOIN Users u ON c.TeacherUserID = u.UserID WHERE c.CourseID = @CourseID", conn))
                            {
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                using (SqlDataReader reader = cmd.ExecuteReader()) { if (reader.Read()) currentCourse = new CoursePoco { CourseID = (int)reader["CourseID"], Title = reader["Title"].ToString(), CourseCode = reader["CourseCode"].ToString(), TeacherName = reader["TeacherName"].ToString() }; }
                            }
                            if (currentCourse != null)
                            {
                                using (SqlCommand cmd = new SqlCommand("SELECT ModuleID, Title, IsPublished, Description, SortOrder FROM CourseModules WHERE CourseID = @CourseID ORDER BY SortOrder", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            bool isPublished = (bool)reader["IsPublished"];
                                            if (isUserTeacher || user.RoleName == "Administrator" || isPublished)
                                            {
                                                modules.Add(new CourseModulePoco {
                                                    ModuleID = (int)reader["ModuleID"],
                                                    Title = reader["Title"].ToString(),
                                                    IsPublished = isPublished,
                                                    Description = reader["Description"] as string,
                                                    SortOrder = (int)reader["SortOrder"]
                                                });
                                            }
                                        }
                                    }
                                }

                                foreach (CourseModulePoco module in modules)
                                {
                                    using (SqlCommand cmd = new SqlCommand("SELECT ContentID, Title, ContentType, ContentPath, IsVisible, SortOrder FROM CourseContent WHERE ModuleID = @ModuleID ORDER BY SortOrder", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@ModuleID", module.ModuleID);
                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                bool isVisible = (bool)reader["IsVisible"];
                                                if (isUserTeacher || user.RoleName == "Administrator" || isVisible)
                                                {
                                                    module.Contents.Add(new CourseContentPoco {
                                                        ContentID = (int)reader["ContentID"],
                                                        Title = reader["Title"].ToString(),
                                                        ContentType = reader["ContentType"].ToString(),
                                                        ContentPath = reader["ContentPath"].ToString(),
                                                        IsVisible = isVisible,
                                                        SortOrder = (int)reader["SortOrder"]
                                                    });
                                                }
                                            }
                                        }
                                    }
                                }
                                using (SqlCommand cmd = new SqlCommand("SELECT e.EvaluationID, e.Title, e.Instructions, e.PointsPossible, e.DueDate, e.IsPublished FROM Evaluations e WHERE e.CourseID = @CourseID ORDER BY e.DueDate", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            bool isPublished = (bool)reader["IsPublished"];
                                            if (isUserTeacher || user.RoleName == "Administrator" || isPublished)
                                            {
                                                evaluations.Add(new EvaluationPoco {
                                                    EvaluationID = (int)reader["EvaluationID"],
                                                    Title = reader["Title"].ToString(),
                                                    Instructions = reader["Instructions"].ToString(),
                                                    PointsPossible = (decimal)reader["PointsPossible"],
                                                    DueDate = (DateTime)reader["DueDate"],
                                                    IsPublished = isPublished
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        StringBuilder body = new StringBuilder();
                        body.Append("<h3>" + currentCourse.CourseCode + " - Taught by " + currentCourse.TeacherName + "</h3>");
                        body.Append("<div class='course-navigation'>");
                        body.Append("<a href='/course/calendar?id=" + courseId + "' class='button'>📅 Calendar</a> ");
                        if (isUserTeacher || user.RoleName == "Administrator")
                        {
                            body.Append("<a href='/course/roster?id=" + courseId + "' class='button'>View Roster</a> ");
                            body.Append("<a href='/course/evaluation/create?Id=" + courseId + "' class='button button-create'>New Evaluation</a>");
                        }
                        body.Append("</div>");
                        if (isUserTeacher || user.RoleName == "Administrator")
                        {
                            body.Append("<div style='display:flex;justify-content:space-between;align-items:center;margin-bottom:20px;'>");
                            body.Append("<h4>Course Modules</h4>");
                            body.Append("<div>");
                            body.Append("<a href='/course/roster?id=" + courseId + "' class='button' style='margin-right:10px;'>View Roster</a>");
                            body.Append("<button onclick='showAddModuleModal()' class='button button-create'>Add Module</button>");
                            body.Append("</div>");
                            body.Append("</div>");
                        }
                        else
                        {
                            body.Append("<div style='margin-bottom:20px;'>");
                            body.Append("<h4>Course Content</h4>");
                            if (currentCourse.IsArchived)
                            {
                                body.Append("<div style='background:#f39c12;color:white;padding:10px;border-radius:4px;margin-bottom:15px;'>");
                                body.Append("📚 This course has been archived. Content is read-only.");
                                body.Append("</div>");
                            }
                            body.Append("</div>");
                        }

                        if (modules.Count == 0)
                        {
                            body.Append("<div class='card'>");
                            if (isUserTeacher || user.RoleName == "Administrator")
                            {
                                body.Append("<p>No modules have been created yet. Click 'Add Module' to get started.</p>");
                            }
                            else
                            {
                                body.Append("<p>No course content is available yet.</p>");
                            }
                            body.Append("</div>");
                        }
                        else
                        {
                            if (isUserTeacher || user.RoleName == "Administrator")
                            {
                                body.Append("<div id='moduleList' class='sortable-list'>");
                            }

                            foreach (CourseModulePoco module in modules)
                            {
                                string moduleClass = "card module-item";
                                if (isUserTeacher || user.RoleName == "Administrator")
                                {
                                    moduleClass += " draggable";
                                    if (!module.IsPublished) moduleClass += " unpublished";
                                }

                                body.Append("<div class='" + moduleClass + "' data-module-id='" + module.ModuleID + "'>");

                                if (isUserTeacher || user.RoleName == "Administrator")
                                {
                                    body.Append("<div style='display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:10px;'>");
                                    body.Append("<div style='flex:1;'>");
                                }

                                body.Append("<h3>" + module.Title);
                                if (isUserTeacher || user.RoleName == "Administrator")
                                {
                                    body.Append(" <span class='module-status'>" + (module.IsPublished ? "(Published)" : "(Hidden from students)") + "</span>");
                                }
                                body.Append("</h3>");

                                if (!string.IsNullOrEmpty(module.Description))
                                {
                                    body.Append("<p class='module-description'>" + module.Description + "</p>");
                                }

                                if (isUserTeacher || user.RoleName == "Administrator")
                                {
                                    body.Append("</div>");
                                    body.Append("<div class='module-controls'>");
                                    body.Append("<button onclick='editModule(" + module.ModuleID + ", \"" + module.Title.Replace("\"", "&quot;") + "\", \"" + (module.Description ?? "").Replace("\"", "&quot;") + "\")' class='button-small'>Edit</button> ");
                                    body.Append("<form method='post' action='/course/toggle-module?id=" + courseId + "' style='display:inline;'>");
                                    body.Append("<input type='hidden' name='moduleId' value='" + module.ModuleID + "'>");
                                    body.Append("<input type='submit' value='" + (module.IsPublished ? "Hide" : "Publish") + "' class='button-small " + (module.IsPublished ? "button-warning" : "button-success") + "'>");
                                    body.Append("</form> ");
                                    body.Append("<form method='post' action='/course/delete-module?id=" + courseId + "' style='display:inline;' onsubmit='return confirm(\"Are you sure you want to delete this module? All content within it will also be permanently deleted.\");'>");
                                    body.Append("<input type='hidden' name='moduleId' value='" + module.ModuleID + "'>");
                                    body.Append("<input type='submit' value='Delete' class='button-small button-danger'>");
                                    body.Append("</form>");
                                    body.Append("</div>");
                                    body.Append("</div>");
                                }

                                if (module.Contents.Count > 0)
                                {
                                    if (isUserTeacher || user.RoleName == "Administrator")
                                    {
                                        body.Append("<div class='content-list sortable-content' data-module-id='" + module.ModuleID + "'>");
                                    }
                                    else
                                    {
                                        body.Append("<ul class='content-list'>");
                                    }

                                    foreach (CourseContentPoco content in module.Contents)
                                    {
                                        string contentClass = isUserTeacher || user.RoleName == "Administrator" ? "content-item draggable" : "";
                                        if (!content.IsVisible && (isUserTeacher || user.RoleName == "Administrator")) contentClass += " content-hidden-item";

                                        if (isUserTeacher || user.RoleName == "Administrator")
                                        {
                                            body.Append("<div class='" + contentClass + "' data-content-id='" + content.ContentID + "'>");
                                            body.Append("<div style='display:flex;justify-content:space-between;align-items:center;'>");
                                            body.Append("<div style='flex:1;'>");
                                        }
                                        else
                                        {
                                            body.Append("<li class='student-content-item'>");
                                        }

                                        string contentIcon = "";
                                        string contentTarget = "";
                                        string contentHref = content.ContentPath;

                                        if (content.ContentType == "File")
                                        {
                                            contentIcon = "📄";
                                            if (content.ContentPath.StartsWith("data:"))
                                            {
                                                contentHref = "/course/download-content?id=" + courseId + "&contentId=" + content.ContentID;
                                            }
                                        }
                                        else if (content.ContentType == "URL")
                                        {
                                            contentIcon = "🔗";
                                            contentTarget = " target='_blank'";
                                        }

                                        body.Append("<span class='content-icon'>" + contentIcon + "</span>");
                                        body.Append("<a href='" + contentHref + "'" + contentTarget + " class='content-link'>" + content.Title + "</a>");

                                        if ((isUserTeacher || user.RoleName == "Administrator") && !content.IsVisible)
                                        {
                                            body.Append(" <span class='content-hidden'>(Hidden)</span>");
                                        }

                                        if (isUserTeacher || user.RoleName == "Administrator")
                                        {
                                            body.Append("</div>");
                                            body.Append("<div class='content-controls'>");
                                            body.Append("<a href='/course/edit-content?id=" + courseId + "&contentId=" + content.ContentID + "' class='button-small'>Edit</a> ");
                                            body.Append("<form method='post' action='/course/toggle-content?id=" + courseId + "' style='display:inline;'>");
                                            body.Append("<input type='hidden' name='contentId' value='" + content.ContentID + "'>");
                                            body.Append("<input type='submit' value='" + (content.IsVisible ? "Hide" : "Show") + "' class='button-small " + (content.IsVisible ? "button-warning" : "button-success") + "'>");
                                            body.Append("</form> ");
                                            body.Append("<form method='post' action='/course/delete-content?id=" + courseId + "' style='display:inline;' onsubmit='return confirm(\"Are you sure you want to delete this resource?\");'>");
                                            body.Append("<input type='hidden' name='contentId' value='" + content.ContentID + "'>");
                                            body.Append("<input type='submit' value='Delete' class='button-small button-danger'>");
                                            body.Append("</form>");
                                            body.Append("</div>");
                                            body.Append("</div>");
                                            body.Append("</div>");
                                        }
                                        else
                                        {
                                            body.Append("</li>");
                                        }
                                    }

                                    if (isUserTeacher || user.RoleName == "Administrator")
                                    {
                                        body.Append("</div>");
                                    }
                                    else
                                    {
                                        body.Append("</ul>");
                                    }
                                }

                                // Display evaluations for this module
                                List<EvaluationPoco> moduleEvaluations = evaluations;

                                if (moduleEvaluations.Count > 0)
                                {
                                    body.Append("<div class='evaluation-list'>");
                                    foreach (EvaluationPoco evaluation in moduleEvaluations)
                                    {
                                        body.Append("<div class='evaluation-item" + (!evaluation.IsPublished && (isUserTeacher || user.RoleName == "Administrator") ? " evaluation-hidden" : "") + "'>");
                                        body.Append("<div class='evaluation-main'>");
                                        body.Append("<span class='evaluation-icon'>📋</span>");
                                        body.Append("<a href='/evaluation/details?id=" + evaluation.EvaluationID + "' class='evaluation-link'>");
                                        body.Append(evaluation.Title);
                                        body.Append("</a>");
                                        body.Append("<span class='evaluation-due'> - Due: " + evaluation.DueDate.ToString("MMM dd, yyyy HH:mm") + "</span>");
                                        body.Append("<span class='evaluation-points'> (" + evaluation.PointsPossible + " pts)</span>");

                                        if ((isUserTeacher || user.RoleName == "Administrator") && !evaluation.IsPublished)
                                        {
                                            body.Append(" <span class='evaluation-hidden-text'>(Hidden)</span>");
                                        }
                                        body.Append("</div>");

                                        if (isUserTeacher || user.RoleName == "Administrator")
                                        {
                                            body.Append("<div class='evaluation-actions'>");
                                            body.Append("<a href='/evaluation/edit?id=" + evaluation.EvaluationID + "' class='button button-small'>Edit</a>");

                                            string visibilityAction = evaluation.IsPublished ? "Hide" : "Show";
                                            body.Append("<form method='post' action='/evaluation/toggle-visibility' style='display:inline;margin-left:5px;'>");
                                            body.Append("<input type='hidden' name='evaluationId' value='" + evaluation.EvaluationID + "'>");
                                            body.Append("<input type='hidden' name='courseId' value='" + courseId + "'>");
                                            body.Append("<input type='submit' value='" + visibilityAction + "' class='button button-small button-secondary'>");
                                            body.Append("</form>");

                                            body.Append("<form method='post' action='/evaluation/delete' style='display:inline;margin-left:5px;' onsubmit='return confirm(\"Are you sure you want to delete this evaluation? This action cannot be undone.\");'>");
                                            body.Append("<input type='hidden' name='evaluationId' value='" + evaluation.EvaluationID + "'>");
                                            body.Append("<input type='hidden' name='courseId' value='" + courseId + "'>");
                                            body.Append("<input type='submit' value='Delete' class='button button-small button-danger'>");
                                            body.Append("</form>");
                                            body.Append("</div>");
                                        }

                                        body.Append("</div>");
                                    }
                                    body.Append("</div>");
                                }

                                if (isUserTeacher || user.RoleName == "Administrator")
                                {
                                    body.Append("<div class='add-content-form'>");
                                    body.Append("<a href='/course/add-content?id=" + courseId + "&moduleId=" + module.ModuleID + "' class='button button-create' style='margin-right:10px;'>Add Resource</a>");
                                    body.Append("<a href='/course/evaluation/create?id=" + courseId + "' class='button button-create'>Add Evaluation</a>");
                                    body.Append("</div>");
                                }

                                body.Append("</div>");
                            }

                            if (isUserTeacher || user.RoleName == "Administrator")
                            {
                                body.Append("</div>");
                            }
                        }
                        // Evaluations are now displayed within their respective modules above

                        if (isUserTeacher || user.RoleName == "Administrator")
                        {
                            body.Append("<div id='addModuleModal' style='display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:1000;'>");
                            body.Append("<div style='position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border-radius:5px;width:500px;'>");
                            body.Append("<h3>Add New Module</h3>");
                            body.Append("<form method='post' action='/course/add-module?id=" + courseId + "'>");
                            body.Append("<label>Module Title:</label>");
                            body.Append("<input type='text' name='moduleTitle' required style='width:100%;margin-bottom:15px;'>");
                            body.Append("<label>Module Description (optional):</label>");
                            body.Append("<textarea name='moduleDescription' rows='4' style='width:100%;margin-bottom:15px;'></textarea>");
                            body.Append("<div style='text-align:right;'>");
                            body.Append("<button type='button' onclick='hideAddModuleModal()' style='margin-right:10px;'>Cancel</button>");
                            body.Append("<input type='submit' value='Create Module' class='button'>");
                            body.Append("</div>");
                            body.Append("</form>");
                            body.Append("</div>");
                            body.Append("</div>");

                            body.Append("<div id='editModuleModal' style='display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:1000;'>");
                            body.Append("<div style='position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border-radius:5px;width:500px;'>");
                            body.Append("<h3>Edit Module</h3>");
                            body.Append("<form method='post' action='/course/edit-module?id=" + courseId + "'>");
                            body.Append("<input type='hidden' id='editModuleId' name='moduleId'>");
                            body.Append("<label>Module Title:</label>");
                            body.Append("<input type='text' id='editModuleTitle' name='moduleTitle' required style='width:100%;margin-bottom:15px;'>");
                            body.Append("<label>Module Description (optional):</label>");
                            body.Append("<textarea id='editModuleDescription' name='moduleDescription' rows='4' style='width:100%;margin-bottom:15px;'></textarea>");
                            body.Append("<div style='text-align:right;'>");
                            body.Append("<button type='button' onclick='hideEditModuleModal()' style='margin-right:10px;'>Cancel</button>");
                            body.Append("<input type='submit' value='Update Module' class='button'>");
                            body.Append("</div>");
                            body.Append("</form>");
                            body.Append("</div>");
                            body.Append("</div>");

                            body.Append("<style>");
                            body.Append(".module-item { position: relative; margin-bottom: 15px; }");
                            body.Append(".module-item.unpublished { opacity: 0.7; border-left: 4px solid #f39c12; }");
                            body.Append(".module-item.draggable { cursor: move; }");
                            body.Append(".module-status { font-size: 0.8em; color: #666; font-weight: normal; }");
                            body.Append(".module-controls { display: flex; gap: 5px; }");
                            body.Append(".button-small { padding: 4px 8px; font-size: 12px; }");
                            body.Append(".button-success { background: #5cb85c; }");
                            body.Append(".button-warning { background: #f0ad4e; }");
                            body.Append(".button-danger { background: #d9534f; }");
                            body.Append(".content-list { margin: 10px 0; }");
                            body.Append(".content-hidden { color: #999; font-size: 0.9em; }");
                            body.Append(".content-hidden-item { opacity: 0.6; }");
                            body.Append(".content-item { padding: 8px; margin: 4px 0; border: 1px solid #eee; border-radius: 3px; }");
                            body.Append(".content-item.draggable { cursor: move; }");
                            body.Append(".content-controls { display: flex; gap: 5px; }");
                            body.Append(".content-icon { margin-right: 8px; }");
                            body.Append(".add-content-form { border-top: 1px solid #eee; padding-top: 15px; margin-top: 15px; }");
                            body.Append(".sortable-list .module-item { border: 1px solid #ddd; }");
                            body.Append(".sortable-content { list-style: none; padding: 0; }");
                            body.Append(".student-content-item { padding: 8px 0; border-bottom: 1px solid #f0f0f0; }");
                            body.Append(".student-content-item:last-child { border-bottom: none; }");
                            body.Append(".content-link { text-decoration: none; color: #007bff; font-weight: 500; }");
                            body.Append(".content-link:hover { text-decoration: underline; }");
                            body.Append(".evaluation-list { margin: 10px 0; }");
                            body.Append(".evaluation-item { padding: 12px; margin: 8px 0; border: 1px solid #e0e0e0; border-radius: 4px; background: #f9f9f9; }");
                            body.Append(".evaluation-item.evaluation-hidden { opacity: 0.6; border-left: 4px solid #f39c12; }");
                            body.Append(".evaluation-icon { margin-right: 8px; font-size: 1.2em; }");
                            body.Append(".evaluation-link { text-decoration: none; color: #007bff; font-weight: 600; }");
                            body.Append(".evaluation-link:hover { text-decoration: underline; }");
                            body.Append(".evaluation-due { color: #666; font-size: 0.9em; }");
                            body.Append(".evaluation-points { color: #28a745; font-weight: 500; }");
                            body.Append(".evaluation-hidden-text { color: #f39c12; font-size: 0.9em; }");
                            body.Append(".evaluation-main { display: flex; align-items: center; margin-bottom: 8px; }");
                            body.Append(".evaluation-actions { display: flex; gap: 5px; }");
                            body.Append(".button-small { padding: 4px 8px; font-size: 0.8em; }");
                            body.Append(".button-secondary { background: #6c757d; }");
                            body.Append(".button-secondary:hover { background: #5a6268; }");
                            body.Append(".button-danger { background: #dc3545; }");
                            body.Append(".button-danger:hover { background: #c82333; }");
                            body.Append(".course-navigation { margin-bottom: 20px; }");
                            body.Append(".calendar-list { margin: 20px 0; }");
                            body.Append(".calendar-item { display: flex; padding: 15px; margin: 10px 0; border: 1px solid #e0e0e0; border-radius: 6px; background: #fff; }");
                            body.Append(".calendar-item.upcoming { border-left: 4px solid #28a745; }");
                            body.Append(".calendar-item.past { border-left: 4px solid #6c757d; opacity: 0.8; }");
                            body.Append(".calendar-date { min-width: 80px; text-align: center; margin-right: 15px; }");
                            body.Append(".calendar-date .date { display: block; font-weight: bold; font-size: 1.1em; }");
                            body.Append(".calendar-date .time { display: block; color: #666; font-size: 0.9em; }");
                            body.Append(".calendar-details h5 { margin: 0 0 5px 0; }");
                            body.Append(".calendar-details .points { color: #28a745; font-weight: 500; margin: 0; }");
                            body.Append(".due-status.urgent { color: #dc3545; font-weight: bold; }");
                            body.Append(".due-status.normal { color: #28a745; }");
                            body.Append(".due-status.overdue { color: #dc3545; }");
                            body.Append(".submission-status { margin: 10px 0; padding: 10px; border-radius: 4px; }");
                            body.Append(".submission-status.overdue { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }");
                            body.Append(".evaluation-meta { margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; }");
                            body.Append(".evaluation-instructions { margin: 20px 0; }");
                            body.Append(".instructions-content { padding: 15px; background: #fff; border: 1px solid #e0e0e0; border-radius: 4px; line-height: 1.6; }");
                            body.Append(".evaluation-navigation { margin-top: 20px; text-align: center; }");
                            body.Append("</style>");

                            body.Append("<script>");
                            body.Append("function showAddModuleModal() { document.getElementById('addModuleModal').style.display = 'block'; }");
                            body.Append("function hideAddModuleModal() { document.getElementById('addModuleModal').style.display = 'none'; }");
                            body.Append("function editModule(id, title, description) {");
                            body.Append("  document.getElementById('editModuleId').value = id;");
                            body.Append("  document.getElementById('editModuleTitle').value = title;");
                            body.Append("  document.getElementById('editModuleDescription').value = description;");
                            body.Append("  document.getElementById('editModuleModal').style.display = 'block';");
                            body.Append("}");
                            body.Append("function hideEditModuleModal() { document.getElementById('editModuleModal').style.display = 'none'; }");

                            body.Append("var sortable = document.getElementById('moduleList');");
                            body.Append("if (sortable) {");
                            body.Append("  var draggedElement = null;");
                            body.Append("  var modules = sortable.querySelectorAll('.module-item');");
                            body.Append("  modules.forEach(function(module) {");
                            body.Append("    module.draggable = true;");
                            body.Append("    module.addEventListener('dragstart', function(e) {");
                            body.Append("      draggedElement = this;");
                            body.Append("      e.dataTransfer.effectAllowed = 'move';");
                            body.Append("    });");
                            body.Append("    module.addEventListener('dragover', function(e) {");
                            body.Append("      e.preventDefault();");
                            body.Append("      e.dataTransfer.dropEffect = 'move';");
                            body.Append("    });");
                            body.Append("    module.addEventListener('drop', function(e) {");
                            body.Append("      e.preventDefault();");
                            body.Append("      if (this !== draggedElement) {");
                            body.Append("        var allModules = Array.from(sortable.children);");
                            body.Append("        var draggedIndex = allModules.indexOf(draggedElement);");
                            body.Append("        var targetIndex = allModules.indexOf(this);");
                            body.Append("        if (draggedIndex < targetIndex) {");
                            body.Append("          this.parentNode.insertBefore(draggedElement, this.nextSibling);");
                            body.Append("        } else {");
                            body.Append("          this.parentNode.insertBefore(draggedElement, this);");
                            body.Append("        }");
                            body.Append("        saveModuleOrder();");
                            body.Append("      }");
                            body.Append("    });");
                            body.Append("  });");
                            body.Append("}");

                            body.Append("function saveModuleOrder() {");
                            body.Append("  var moduleIds = [];");
                            body.Append("  var modules = document.querySelectorAll('.module-item');");
                            body.Append("  modules.forEach(function(module) {");
                            body.Append("    moduleIds.push(module.getAttribute('data-module-id'));");
                            body.Append("  });");
                            body.Append("  var form = document.createElement('form');");
                            body.Append("  form.method = 'POST';");
                            body.Append("  form.action = '/course/reorder-modules?id=" + courseId + "';");
                            body.Append("  var input = document.createElement('input');");
                            body.Append("  input.type = 'hidden';");
                            body.Append("  input.name = 'moduleIds';");
                            body.Append("  input.value = moduleIds.join(',');");
                            body.Append("  form.appendChild(input);");
                            body.Append("  document.body.appendChild(form);");
                            body.Append("  form.submit();");
                            body.Append("}");

                            body.Append("function saveContentOrder(moduleId) {");
                            body.Append("  var contentIds = [];");
                            body.Append("  var contents = document.querySelectorAll('[data-module-id=\"' + moduleId + '\"] .content-item');");
                            body.Append("  contents.forEach(function(content) {");
                            body.Append("    contentIds.push(content.getAttribute('data-content-id'));");
                            body.Append("  });");
                            body.Append("  var form = document.createElement('form');");
                            body.Append("  form.method = 'POST';");
                            body.Append("  form.action = '/course/reorder-content?id=" + courseId + "';");
                            body.Append("  var input = document.createElement('input');");
                            body.Append("  input.type = 'hidden';");
                            body.Append("  input.name = 'contentIds';");
                            body.Append("  input.value = contentIds.join(',');");
                            body.Append("  form.appendChild(input);");
                            body.Append("  document.body.appendChild(form);");
                            body.Append("  form.submit();");
                            body.Append("}");

                            body.Append("document.querySelectorAll('.sortable-content').forEach(function(contentList) {");
                            body.Append("  var moduleId = contentList.getAttribute('data-module-id');");
                            body.Append("  var contents = contentList.querySelectorAll('.content-item');");
                            body.Append("  contents.forEach(function(content) {");
                            body.Append("    content.draggable = true;");
                            body.Append("    content.addEventListener('dragstart', function(e) {");
                            body.Append("      e.dataTransfer.setData('text/plain', this.getAttribute('data-content-id'));");
                            body.Append("      e.dataTransfer.effectAllowed = 'move';");
                            body.Append("    });");
                            body.Append("    content.addEventListener('dragover', function(e) {");
                            body.Append("      e.preventDefault();");
                            body.Append("      e.dataTransfer.dropEffect = 'move';");
                            body.Append("    });");
                            body.Append("    content.addEventListener('drop', function(e) {");
                            body.Append("      e.preventDefault();");
                            body.Append("      var draggedId = e.dataTransfer.getData('text/plain');");
                            body.Append("      var draggedElement = document.querySelector('[data-content-id=\"' + draggedId + '\"]');");
                            body.Append("      if (this !== draggedElement && this.parentNode === draggedElement.parentNode) {");
                            body.Append("        var allContents = Array.from(this.parentNode.children);");
                            body.Append("        var draggedIndex = allContents.indexOf(draggedElement);");
                            body.Append("        var targetIndex = allContents.indexOf(this);");
                            body.Append("        if (draggedIndex < targetIndex) {");
                            body.Append("          this.parentNode.insertBefore(draggedElement, this.nextSibling);");
                            body.Append("        } else {");
                            body.Append("          this.parentNode.insertBefore(draggedElement, this);");
                            body.Append("        }");
                            body.Append("        saveContentOrder(moduleId);");
                            body.Append("      }");
                            body.Append("    });");
                            body.Append("  });");
                            body.Append("});");
                            body.Append("</script>");
                        }

                        string breadcrumb = "";
                        if (user.RoleName == "Student")
                        {
                            breadcrumb = "<a href='/student/dashboard'>Dashboard</a> <span>></span> <a href='/student/dashboard'>My Courses</a> <span>></span> <span>" + currentCourse.Title + "</span>";
                        }
                        else if (user.RoleName == "Teacher")
                        {
                            breadcrumb = "<a href='/teacher/dashboard'>Dashboard</a> <span>></span> <a href='/teacher/dashboard'>My Courses</a> <span>></span> <span>" + currentCourse.Title + "</span>";
                        }
                        else if (user.RoleName == "Administrator")
                        {
                            breadcrumb = "<a href='/admin/dashboard'>Dashboard</a> <span>></span> <a href='/admin/courses'>Courses</a> <span>></span> <span>" + currentCourse.Title + "</span>";
                        }

                        responseString = RenderPage(currentCourse.Title, body.ToString(), user, message, messageType, breadcrumb);
                    }
                }
                else if (url.StartsWith("/course/download-content"))
                {
                    int contentId = int.Parse(request.QueryString["contentId"]);

                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("SELECT Title, ContentPath FROM CourseContent WHERE ContentID = @ContentID", conn))
                        {
                            cmd.Parameters.AddWithValue("@ContentID", contentId);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    string contentPath = reader["ContentPath"].ToString();
                                    string title = reader["Title"].ToString();

                                    if (contentPath.StartsWith("data:"))
                                    {
                                        string[] parts = contentPath.Split(':');
                                        if (parts.Length >= 3)
                                        {
                                            string filename = parts[1];
                                            string base64Data = parts[2];

                                            try
                                            {
                                                byte[] fileData = Convert.FromBase64String(base64Data);

                                                response.ContentType = "application/octet-stream";
                                                response.Headers.Add("Content-Disposition", "attachment; filename=\"" + filename + "\"");
                                                response.ContentLength64 = fileData.Length;
                                                response.OutputStream.Write(fileData, 0, fileData.Length);
                                                response.OutputStream.Close();
                                                return;
                                            }
                                            catch
                                            {
                                                response.StatusCode = 404;
                                                responseString = "File not found or corrupted.";
                                            }
                                        }
                                    }
                                    else if (contentPath.StartsWith("/") && File.Exists(contentPath.Substring(1)))
                                    {
                                        string filePath = contentPath.Substring(1);
                                        byte[] fileData = File.ReadAllBytes(filePath);
                                        string filename = Path.GetFileName(filePath);

                                        response.ContentType = "application/octet-stream";
                                        response.Headers.Add("Content-Disposition", "attachment; filename=\"" + filename + "\"");
                                        response.ContentLength64 = fileData.Length;
                                        response.OutputStream.Write(fileData, 0, fileData.Length);
                                        response.OutputStream.Close();
                                        return;
                                    }
                                    else
                                    {
                                        response.StatusCode = 404;
                                        responseString = "File not found.";
                                    }
                                }
                                else
                                {
                                    response.StatusCode = 404;
                                    responseString = "Content not found.";
                                }
                            }
                        }
                    }
                }
                else if (url.StartsWith("/evaluation/details"))
                {
                    int evaluationId = int.Parse(request.QueryString["id"]);

                    EvaluationPoco evaluation = null;
                    CoursePoco course = null;
                    bool hasSubmission = false;
                    bool isUserEnrolled = false;
                    bool isUserTeacher = false;

                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();

                        // Get evaluation details with course info
                        using (SqlCommand cmd = new SqlCommand(@"
                            SELECT e.EvaluationID, e.Title, e.Instructions, e.PointsPossible, e.DueDate, e.IsPublished,
                                   c.CourseID, c.Title as CourseTitle, c.CourseCode, c.TeacherUserID,
                                   u.FirstName + ' ' + u.LastName as TeacherName
                            FROM Evaluations e
                            JOIN Courses c ON e.CourseID = c.CourseID
                            JOIN Users u ON c.TeacherUserID = u.UserID
                            WHERE e.EvaluationID = @EvaluationID", conn))
                        {
                            cmd.Parameters.AddWithValue("@EvaluationID", evaluationId);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    bool isPublished = (bool)reader["IsPublished"];
                                    int courseId = (int)reader["CourseID"];
                                    int teacherUserId = (int)reader["TeacherUserID"];

                                    // Check if user is teacher of this course
                                    isUserTeacher = (user.RoleName == "Teacher" && user.UserID == teacherUserId) || user.RoleName == "Administrator";

                                    // Check if user is enrolled in this course (for students)
                                    if (user.RoleName == "Student")
                                    {
                                        using (SqlConnection conn2 = new SqlConnection(_dbConnectionString))
                                        {
                                            conn2.Open();
                                            using (SqlCommand cmd2 = new SqlCommand("SELECT COUNT(*) FROM Enrollments WHERE CourseID = @CourseID AND StudentID = @StudentID", conn2))
                                            {
                                                cmd2.Parameters.AddWithValue("@CourseID", courseId);
                                                cmd2.Parameters.AddWithValue("@StudentID", user.UserID);
                                                isUserEnrolled = (int)cmd2.ExecuteScalar() > 0;
                                            }
                                        }
                                    }

                                    // Check if user has access to this evaluation
                                    if (isUserTeacher || (isPublished && isUserEnrolled))
                                    {
                                        evaluation = new EvaluationPoco
                                        {
                                            EvaluationID = (int)reader["EvaluationID"],
                                            Title = reader["Title"].ToString(),
                                            Instructions = reader["Instructions"].ToString(),
                                            PointsPossible = (decimal)reader["PointsPossible"],
                                            DueDate = (DateTime)reader["DueDate"],
                                            IsPublished = isPublished
                                        };

                                        course = new CoursePoco
                                        {
                                            CourseID = courseId,
                                            Title = reader["CourseTitle"].ToString(),
                                            CourseCode = reader["CourseCode"].ToString(),
                                            TeacherName = reader["TeacherName"].ToString()
                                        };
                                    }
                                }
                            }
                        }

                        // Check if student has already submitted
                        if (evaluation != null && user.RoleName == "Student")
                        {
                            using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Submissions WHERE EvaluationID = @EvaluationID AND StudentID = @StudentID", conn))
                            {
                                cmd.Parameters.AddWithValue("@EvaluationID", evaluationId);
                                cmd.Parameters.AddWithValue("@StudentID", user.UserID);
                                hasSubmission = (int)cmd.ExecuteScalar() > 0;
                            }
                        }
                    }

                    if (evaluation == null)
                    {
                        response.StatusCode = 404;
                        responseString = RenderPage("Not Found", "<div class='card'><h3>Evaluation Not Found</h3><p>The requested evaluation could not be found or you don't have permission to view it.</p></div>", user, "", "");
                    }
                    else
                    {
                        StringBuilder body = new StringBuilder();
                        body.Append("<div class='card'>");
                        body.Append("<h3>" + evaluation.Title + "</h3>");
                        body.Append("<div class='evaluation-meta'>");
                        body.Append("<p><strong>Course:</strong> " + course.CourseCode + " - " + course.Title + "</p>");
                        body.Append("<p><strong>Instructor:</strong> " + course.TeacherName + "</p>");
                        body.Append("<p><strong>Points Possible:</strong> " + evaluation.PointsPossible + "</p>");
                        body.Append("<p><strong>Due Date:</strong> " + evaluation.DueDate.ToString("MMMM dd, yyyy 'at' h:mm tt") + "</p>");

                        if (evaluation.DueDate < DateTime.Now)
                        {
                            body.Append("<p class='due-status overdue'><strong>Status:</strong> Past Due</p>");
                        }
                        else
                        {
                            TimeSpan timeLeft = evaluation.DueDate - DateTime.Now;
                            if (timeLeft.TotalDays < 1)
                            {
                                body.Append("<p class='due-status urgent'><strong>Status:</strong> Due in " + timeLeft.Hours + " hours</p>");
                            }
                            else
                            {
                                body.Append("<p class='due-status normal'><strong>Status:</strong> Due in " + (int)timeLeft.TotalDays + " days</p>");
                            }
                        }
                        body.Append("</div>");

                        if (!string.IsNullOrEmpty(evaluation.Instructions))
                        {
                            body.Append("<div class='evaluation-instructions'>");
                            body.Append("<h4>Instructions</h4>");
                            body.Append("<div class='instructions-content'>" + evaluation.Instructions.Replace("\n", "<br>") + "</div>");
                            body.Append("</div>");
                        }

                        if (user.RoleName == "Student")
                        {
                            body.Append("<div class='evaluation-actions'>");
                            if (hasSubmission)
                            {
                                body.Append("<p class='submission-status'>✓ You have already submitted work for this evaluation.</p>");
                                body.Append("<a href='/evaluation/submission?id=" + evaluationId + "' class='button'>View My Submission</a>");
                            }
                            else if (evaluation.DueDate > DateTime.Now)
                            {
                                body.Append("<a href='/evaluation/submit?id=" + evaluationId + "' class='button button-create'>Submit Work</a>");
                            }
                            else
                            {
                                body.Append("<p class='submission-status overdue'>This evaluation is past due. Submissions are no longer accepted.</p>");
                            }
                            body.Append("</div>");
                        }

                        body.Append("<div class='evaluation-navigation'>");
                        body.Append("<a href='/course?id=" + course.CourseID + "' class='button'>Back to Course</a>");
                        body.Append("</div>");
                        body.Append("</div>");

                        string breadcrumb = "<a href='/course?id=" + course.CourseID + "'>" + course.CourseCode + "</a> <span>></span> <span>" + evaluation.Title + "</span>";
                        responseString = RenderPage("Evaluation Details", body.ToString(), user, message, messageType, breadcrumb);
                    }
                }
                else if (url.StartsWith("/evaluation/edit"))
                {
                    int evaluationId = int.Parse(request.QueryString["id"]);

                    if (method == "GET")
                    {
                        EvaluationPoco evaluation = null;
                        CoursePoco course = null;

                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand(@"
                                SELECT e.EvaluationID, e.Title, e.Instructions, e.PointsPossible, e.DueDate, e.IsPublished,
                                       c.CourseID, c.Title as CourseTitle, c.CourseCode, c.TeacherUserID
                                FROM Evaluations e
                                JOIN Courses c ON e.CourseID = c.CourseID
                                WHERE e.EvaluationID = @EvaluationID", conn))
                            {
                                cmd.Parameters.AddWithValue("@EvaluationID", evaluationId);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        int teacherUserId = (int)reader["TeacherUserID"];
                                        // Verify user has permission to edit this evaluation
                                        if (user.RoleName == "Administrator" || (user.RoleName == "Teacher" && user.UserID == teacherUserId))
                                        {
                                            evaluation = new EvaluationPoco
                                            {
                                                EvaluationID = (int)reader["EvaluationID"],
                                                Title = reader["Title"].ToString(),
                                                Instructions = reader["Instructions"].ToString(),
                                                PointsPossible = (decimal)reader["PointsPossible"],
                                                DueDate = (DateTime)reader["DueDate"],
                                                IsPublished = (bool)reader["IsPublished"]
                                            };

                                            course = new CoursePoco
                                            {
                                                CourseID = (int)reader["CourseID"],
                                                Title = reader["CourseTitle"].ToString(),
                                                CourseCode = reader["CourseCode"].ToString()
                                            };
                                        }
                                    }
                                }
                            }
                        }

                        if (evaluation == null)
                        {
                            response.StatusCode = 404;
                            responseString = RenderPage("Not Found", "<div class='card'><h3>Evaluation Not Found</h3><p>The requested evaluation could not be found or you don't have permission to edit it.</p></div>", user, "", "");
                        }
                        else
                        {
                            StringBuilder body = new StringBuilder();
                            body.Append("<div class='card'>");
                            body.Append("<h3>Edit Evaluation</h3>");
                            body.Append("<form method='post' action='/evaluation/edit?id=" + evaluationId + "'>");

                            body.Append("<label>Title:</label>");
                            body.Append("<input type='text' name='title' value='" + evaluation.Title.Replace("'", "&#39;") + "' required style='width:100%;margin-bottom:15px;'>");

                            body.Append("<label>Instructions:</label>");
                            body.Append("<textarea name='instructions' rows='8' style='width:100%;margin-bottom:15px;' placeholder='Provide detailed instructions for this evaluation...'>" + evaluation.Instructions.Replace("'", "&#39;") + "</textarea>");

                            body.Append("<div style='display:grid;grid-template-columns:1fr 1fr;gap:15px;margin-bottom:15px;'>");
                            body.Append("<div><label>Points Possible:</label><input type='number' step='0.01' name='points' value='" + evaluation.PointsPossible + "' required min='0'></div>");
                            body.Append("<div><label>Due Date & Time:</label><input type='datetime-local' name='dueDate' value='" + evaluation.DueDate.ToString("yyyy-MM-ddTHH:mm") + "' required></div>");
                            body.Append("</div>");

                            body.Append("<label style='display:flex;align-items:center;margin-bottom:15px;'>");
                            body.Append("<input type='checkbox' name='isPublished' value='true'" + (evaluation.IsPublished ? " checked" : "") + " style='width:auto;margin-right:8px;'>");
                            body.Append("Published (students can see this evaluation)");
                            body.Append("</label>");

                            body.Append("<div style='text-align:right;'>");
                            body.Append("<a href='/course?id=" + course.CourseID + "' style='margin-right:10px;'>Cancel</a>");
                            body.Append("<input type='submit' value='Save Changes' class='button'>");
                            body.Append("</div>");

                            body.Append("</form>");
                            body.Append("</div>");

                            string breadcrumb = "<a href='/course?id=" + course.CourseID + "'>" + course.CourseCode + "</a> <span>></span> <span>Edit Evaluation</span>";
                            responseString = RenderPage("Edit Evaluation", body.ToString(), user, message, messageType, breadcrumb);
                        }
                    }
                    else // POST
                    {
                        string title = formData["title"];
                        string instructions = formData["instructions"] ?? "";
                        string pointsStr = formData["points"];
                        string dueDateStr = formData["dueDate"];
                        bool isPublished = formData["isPublished"] == "true";

                        if (!string.IsNullOrEmpty(title) && !string.IsNullOrEmpty(pointsStr) && !string.IsNullOrEmpty(dueDateStr))
                        {
                            decimal points = decimal.Parse(pointsStr);
                            DateTime dueDate = DateTime.Parse(dueDateStr);

                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();

                                // Verify user has permission to edit this evaluation
                                int courseIdFromEval = 0;
                                using (SqlCommand cmd = new SqlCommand("SELECT c.CourseID FROM Evaluations e JOIN Courses c ON e.CourseID = c.CourseID WHERE e.EvaluationID = @EvaluationID AND (c.TeacherUserID = @UserID OR @IsAdmin = 1)", conn))
                                {
                                    cmd.Parameters.AddWithValue("@EvaluationID", evaluationId);
                                    cmd.Parameters.AddWithValue("@UserID", user.UserID);
                                    cmd.Parameters.AddWithValue("@IsAdmin", user.RoleName == "Administrator" ? 1 : 0);
                                    object result = cmd.ExecuteScalar();
                                    if (result != null)
                                    {
                                        courseIdFromEval = (int)result;
                                    }
                                }

                                if (courseIdFromEval > 0)
                                {
                                    using (SqlCommand cmd = new SqlCommand("UPDATE Evaluations SET Title = @Title, Instructions = @Instructions, PointsPossible = @Points, DueDate = @DueDate, IsPublished = @IsPublished WHERE EvaluationID = @EvaluationID", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@Title", title);
                                        cmd.Parameters.AddWithValue("@Instructions", instructions);
                                        cmd.Parameters.AddWithValue("@Points", points);
                                        cmd.Parameters.AddWithValue("@DueDate", dueDate);
                                        cmd.Parameters.AddWithValue("@IsPublished", isPublished);
                                        cmd.Parameters.AddWithValue("@EvaluationID", evaluationId);
                                        cmd.ExecuteNonQuery();
                                    }

                                    isRedirect = true;
                                    response.RedirectLocation = "/course?id=" + courseIdFromEval + "&msg=Evaluation+updated+successfully.&type=success";
                                }
                                else
                                {
                                    response.StatusCode = 403;
                                    responseString = RenderPage("Access Denied", "<div class='card'><h3>Access Denied</h3><p>You don't have permission to edit this evaluation.</p></div>", user, "", "");
                                }
                            }
                        }
                        else
                        {
                            isRedirect = true;
                            response.RedirectLocation = "/evaluation/edit?id=" + evaluationId + "&msg=All+required+fields+must+be+filled.&type=error";
                        }
                    }
                }
                else if (url.StartsWith("/evaluation/delete") && method == "POST")
                {
                    int evaluationId = int.Parse(formData["evaluationId"]);
                    int courseIdFromForm = int.Parse(formData["courseId"]);

                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();

                        // Verify user has permission to delete this evaluation
                        bool hasPermission = false;
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Evaluations e JOIN Courses c ON e.CourseID = c.CourseID WHERE e.EvaluationID = @EvaluationID AND e.CourseID = @CourseID AND (c.TeacherUserID = @UserID OR @IsAdmin = 1)", conn))
                        {
                            cmd.Parameters.AddWithValue("@EvaluationID", evaluationId);
                            cmd.Parameters.AddWithValue("@CourseID", courseIdFromForm);
                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                            cmd.Parameters.AddWithValue("@IsAdmin", user.RoleName == "Administrator" ? 1 : 0);
                            hasPermission = (int)cmd.ExecuteScalar() > 0;
                        }

                        if (hasPermission)
                        {
                            // Check if there are any submissions for this evaluation
                            int submissionCount = 0;
                            using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Submissions WHERE EvaluationID = @EvaluationID", conn))
                            {
                                cmd.Parameters.AddWithValue("@EvaluationID", evaluationId);
                                submissionCount = (int)cmd.ExecuteScalar();
                            }

                            if (submissionCount > 0)
                            {
                                isRedirect = true;
                                response.RedirectLocation = "/course?id=" + courseIdFromForm + "&msg=This+evaluation+cannot+be+deleted+because+it+contains+student+submissions.+Please+grade+or+remove+submissions+first.&type=error";
                            }
                            else
                            {
                                // Delete the evaluation
                                using (SqlCommand cmd = new SqlCommand("DELETE FROM Evaluations WHERE EvaluationID = @EvaluationID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@EvaluationID", evaluationId);
                                    cmd.ExecuteNonQuery();
                                }

                                isRedirect = true;
                                response.RedirectLocation = "/course?id=" + courseIdFromForm + "&msg=Evaluation+deleted+successfully.&type=success";
                            }
                        }
                        else
                        {
                            isRedirect = true;
                            response.RedirectLocation = "/course?id=" + courseIdFromForm + "&msg=You+don't+have+permission+to+delete+this+evaluation.&type=error";
                        }
                    }
                }
                else if (url.StartsWith("/evaluation/toggle-visibility") && method == "POST")
                {
                    int evaluationId = int.Parse(formData["evaluationId"]);
                    int courseIdFromForm = int.Parse(formData["courseId"]);

                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();

                        // Verify user has permission and get current visibility status
                        bool currentVisibility = false;
                        bool hasPermission = false;
                        using (SqlCommand cmd = new SqlCommand("SELECT e.IsPublished FROM Evaluations e JOIN Courses c ON e.CourseID = c.CourseID WHERE e.EvaluationID = @EvaluationID AND e.CourseID = @CourseID AND (c.TeacherUserID = @UserID OR @IsAdmin = 1)", conn))
                        {
                            cmd.Parameters.AddWithValue("@EvaluationID", evaluationId);
                            cmd.Parameters.AddWithValue("@CourseID", courseIdFromForm);
                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                            cmd.Parameters.AddWithValue("@IsAdmin", user.RoleName == "Administrator" ? 1 : 0);
                            object result = cmd.ExecuteScalar();
                            if (result != null)
                            {
                                hasPermission = true;
                                currentVisibility = (bool)result;
                            }
                        }

                        if (hasPermission)
                        {
                            // Toggle visibility
                            using (SqlCommand cmd = new SqlCommand("UPDATE Evaluations SET IsPublished = @IsPublished WHERE EvaluationID = @EvaluationID", conn))
                            {
                                cmd.Parameters.AddWithValue("@IsPublished", !currentVisibility);
                                cmd.Parameters.AddWithValue("@EvaluationID", evaluationId);
                                cmd.ExecuteNonQuery();
                            }

                            string action = currentVisibility ? "hidden" : "shown";
                            isRedirect = true;
                            response.RedirectLocation = "/course?id=" + courseIdFromForm + "&msg=Evaluation+visibility+updated+-+now+" + action + ".&type=success";
                        }
                        else
                        {
                            isRedirect = true;
                            response.RedirectLocation = "/course?id=" + courseIdFromForm + "&msg=You+don't+have+permission+to+modify+this+evaluation.&type=error";
                        }
                    }
                }
                else if (url == "/teacher/dashboard")
                {
                    StringBuilder body = new StringBuilder();
                    string showArchived = request.QueryString["archived"] ?? "false";

                    if (showArchived == "false")
                    {
                        body.Append("<div style='margin-bottom:20px;'>");
                        body.Append("<a href='/teacher/courses/create' class='button button-create'>Create New Course</a>");
                        body.Append("<a href='/teacher/dashboard?archived=true' class='button' style='margin-left:10px;'>View Archived Courses</a>");
                        body.Append("</div>");
                    }
                    else
                    {
                        body.Append("<div style='margin-bottom:20px;'>");
                        body.Append("<a href='/teacher/dashboard' class='button'>Back to Active Courses</a>");
                        body.Append("</div>");
                    }

                    List<CoursePoco> courses = new List<CoursePoco>();
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        string sql = "SELECT c.CourseID, c.CourseCode, c.Title, c.IsArchived, COUNT(e.StudentID) as StudentCount FROM Courses c LEFT JOIN Enrollments e ON c.CourseID = e.CourseID WHERE c.TeacherUserID = @UserID AND c.IsArchived = @IsArchived GROUP BY c.CourseID, c.CourseCode, c.Title, c.IsArchived ORDER BY c.Title";
                        using (SqlCommand cmd = new SqlCommand(sql, conn))
                        {
                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                            cmd.Parameters.AddWithValue("@IsArchived", showArchived == "true");
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    courses.Add(new CoursePoco {
                                        CourseID = (int)reader["CourseID"],
                                        CourseCode = reader["CourseCode"].ToString(),
                                        Title = reader["Title"].ToString(),
                                        IsArchived = (bool)reader["IsArchived"],
                                        StudentCount = (int)reader["StudentCount"]
                                    });
                                }
                            }
                        }
                    }

                    if (courses.Count == 0)
                    {
                        if (showArchived == "false")
                        {
                            body.Append("<div class='card'>");
                            body.Append("<p>You are not currently managing any active courses.</p>");
                            body.Append("</div>");
                        }
                        else
                        {
                            body.Append("<div class='card'>");
                            body.Append("<p>You have no archived courses.</p>");
                            body.Append("</div>");
                        }
                    }
                    else
                    {
                        body.Append("<div class='card'>");
                        body.Append("<h3>" + (showArchived == "false" ? "Active Courses" : "Archived Courses") + "</h3>");
                        body.Append("<table><tr><th>Course Code</th><th>Title</th><th>Students</th><th>Actions</th></tr>");
                        foreach (CoursePoco c in courses)
                        {
                            body.Append("<tr>");
                            body.Append("<td>" + c.CourseCode + "</td>");
                            body.Append("<td>" + c.Title + "</td>");
                            body.Append("<td>" + c.StudentCount + "</td>");
                            body.Append("<td>");
                            body.Append("<a href='/course?id=" + c.CourseID + "' class='button'>View</a> ");
                            body.Append("<a href='/teacher/courses/edit?id=" + c.CourseID + "' class='button'>Edit Settings</a>");
                            if (showArchived == "true")
                            {
                                body.Append(" <a href='/teacher/courses/restore?id=" + c.CourseID + "' class='button' style='background:#5cb85c;' onclick='return confirm(\"Restore this course to active status?\");'>Restore</a>");
                            }
                            body.Append("</td>");
                            body.Append("</tr>");
                        }
                        body.Append("</table>");
                        body.Append("</div>");
                    }

                    responseString = RenderPage("My Courses", body.ToString(), user, message, messageType);
                }
                else if (url == "/student/dashboard")
                {
                    StringBuilder body = new StringBuilder();
                    string showArchived = request.QueryString["archived"] ?? "false";

                    body.Append("<div style='margin-bottom:20px;'>");
                    if (showArchived == "false")
                    {
                        body.Append("<a href='/student/dashboard?archived=true' class='button'>View Archived Courses</a>");
                    }
                    else
                    {
                        body.Append("<a href='/student/dashboard' class='button'>Back to Active Courses</a>");
                    }
                    body.Append("</div>");

                    List<CoursePoco> courses = new List<CoursePoco>();
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        string sql = "SELECT c.CourseID, c.CourseCode, c.Title, c.IsArchived, u.FirstName + ' ' + u.LastName as TeacherName FROM Courses c JOIN Enrollments e ON c.CourseID = e.CourseID JOIN Users u ON c.TeacherUserID = u.UserID WHERE e.StudentID = @UserID AND c.IsArchived = @IsArchived ORDER BY c.Title";
                        using (SqlCommand cmd = new SqlCommand(sql, conn))
                        {
                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                            cmd.Parameters.AddWithValue("@IsArchived", showArchived == "true");
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    courses.Add(new CoursePoco {
                                        CourseID = (int)reader["CourseID"],
                                        CourseCode = reader["CourseCode"].ToString(),
                                        Title = reader["Title"].ToString(),
                                        IsArchived = (bool)reader["IsArchived"],
                                        TeacherName = reader["TeacherName"].ToString()
                                    });
                                }
                            }
                        }
                    }

                    if (courses.Count == 0)
                    {
                        body.Append("<div class='card'>");
                        if (showArchived == "false")
                        {
                            body.Append("<h3>Welcome to Your Dashboard!</h3>");
                            body.Append("<p>You are not yet enrolled in any courses. Visit the <a href='/student/catalog'>Course Catalog</a> to find and enroll in available courses.</p>");
                        }
                        else
                        {
                            body.Append("<h3>No Archived Courses</h3>");
                            body.Append("<p>You don't have any archived courses yet.</p>");
                        }
                        body.Append("</div>");
                    }
                    else
                    {
                        body.Append("<div class='card'>");
                        body.Append("<h3>" + (showArchived == "false" ? "My Active Courses" : "My Archived Courses") + "</h3>");
                        body.Append("<div class='course-grid'>");

                        foreach (CoursePoco course in courses)
                        {
                            body.Append("<div class='course-card" + (course.IsArchived ? " archived-course" : "") + "'>");
                            body.Append("<div class='course-header'>");
                            body.Append("<h4>" + course.CourseCode + "</h4>");
                            if (course.IsArchived)
                            {
                                body.Append("<span class='course-status archived'>Archived</span>");
                            }
                            body.Append("</div>");
                            body.Append("<h5>" + course.Title + "</h5>");
                            body.Append("<p class='course-teacher'>Instructor: " + course.TeacherName + "</p>");
                            body.Append("<div class='course-actions'>");
                            body.Append("<a href='/course?id=" + course.CourseID + "' class='button course-button'>Enter Course</a>");
                            body.Append("</div>");
                            body.Append("</div>");
                        }

                        body.Append("</div>");
                        body.Append("</div>");
                    }

                    body.Append("<style>");
                    body.Append(".course-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin-top: 20px; }");
                    body.Append(".course-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s; }");
                    body.Append(".course-card:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.15); }");
                    body.Append(".course-card.archived-course { opacity: 0.8; border-left: 4px solid #f39c12; }");
                    body.Append(".course-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }");
                    body.Append(".course-header h4 { margin: 0; color: #333; font-size: 1.2em; }");
                    body.Append(".course-status { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }");
                    body.Append(".course-status.archived { background: #f39c12; color: white; }");
                    body.Append(".course-card h5 { margin: 0 0 10px 0; color: #555; font-size: 1.1em; }");
                    body.Append(".course-teacher { color: #666; font-size: 0.9em; margin: 10px 0; }");
                    body.Append(".course-actions { margin-top: 15px; }");
                    body.Append(".course-button { width: 100%; text-align: center; }");
                    body.Append("</style>");

                    responseString = RenderPage("My Courses", body.ToString(), user, message, messageType);
                }
                else if (url == "/profile")
                {
                    if (method == "GET")
                    {
                        StringBuilder body = new StringBuilder();
                        bool isEditing = request.QueryString["edit"] == "true";

                        if (isEditing)
                        {
                            body.Append("<div class='card'><h3>Edit My Details</h3>");
                            body.Append("<div style='margin-bottom:20px;'>");
                            body.Append("<label>Profile Picture:</label>");
                            body.Append("<div style='display:flex;align-items:center;gap:15px;margin-top:10px;'>");
                            if (!string.IsNullOrEmpty(user.ProfilePicturePath) && File.Exists("uploads/" + user.ProfilePicturePath))
                            {
                                body.Append("<img src='/uploads/" + user.ProfilePicturePath + "' alt='Profile Picture' style='width:80px;height:80px;border-radius:50%;object-fit:cover;border:2px solid #ddd;'>");
                            }
                            else
                            {
                                body.Append("<div style='width:80px;height:80px;border-radius:50%;background:#f0f0f0;display:flex;align-items:center;justify-content:center;border:2px solid #ddd;font-size:24px;color:#666;'>" + user.FirstName.Substring(0, 1) + user.LastName.Substring(0, 1) + "</div>");
                            }
                            body.Append("<div>");
                            body.Append("<div style='font-size:12px;color:#666;margin-bottom:5px;'>Picture upload coming soon</div>");

                            body.Append("</div>");
                            body.Append("</div>");
                            body.Append("</div>");

                            body.Append("<form method='post' action='/profile'>");
                            body.Append("<input type='hidden' name='action' value='updateProfile'>");
                            body.Append("<label for='firstName'>First Name:</label>");
                            body.Append("<input type='text' id='firstName' name='firstName' value='" + user.FirstName + "' required>");
                            body.Append("<label for='lastName'>Last Name:</label>");
                            body.Append("<input type='text' id='lastName' name='lastName' value='" + user.LastName + "' required>");
                            body.Append("<label for='email'>Email Address:</label>");
                            body.Append("<input type='email' id='email' name='email' value='" + user.Email + "' readonly style='background-color:#f5f5f5;'>");
                            body.Append("<label for='role'>Role:</label>");
                            body.Append("<input type='text' id='role' name='role' value='" + user.RoleName + "' readonly style='background-color:#f5f5f5;'>");
                            body.Append("<div style='margin-top:20px;'>");
                            body.Append("<input type='submit' value='Save Changes' style='margin-right:10px;'>");
                            body.Append("<a href='/profile' class='button' style='background:#666;'>Cancel</a>");
                            body.Append("</div>");
                            body.Append("</form></div>");
                        }
                        else
                        {
                            body.Append("<div class='card'><h3>My Details</h3>");
                            body.Append("<div style='display:flex;align-items:center;gap:20px;margin-bottom:20px;'>");
                            if (!string.IsNullOrEmpty(user.ProfilePicturePath) && File.Exists("uploads/" + user.ProfilePicturePath))
                            {
                                body.Append("<img src='/uploads/" + user.ProfilePicturePath + "' alt='Profile Picture' style='width:100px;height:100px;border-radius:50%;object-fit:cover;border:3px solid #ddd;'>");
                            }
                            else
                            {
                                body.Append("<div style='width:100px;height:100px;border-radius:50%;background:#f0f0f0;display:flex;align-items:center;justify-content:center;border:3px solid #ddd;font-size:36px;color:#666;'>" + user.FirstName.Substring(0, 1) + user.LastName.Substring(0, 1) + "</div>");
                            }
                            body.Append("<div>");
                            body.Append("<p><strong>Name:</strong> " + user.FirstName + " " + user.LastName + "</p>");
                            body.Append("<p><strong>Email:</strong> " + user.Email + "</p>");
                            body.Append("<p><strong>Role:</strong> " + user.RoleName + "</p>");
                            body.Append("</div>");
                            body.Append("</div>");
                            body.Append("<a href='/profile?edit=true' class='button'>Edit Profile</a>");
                            body.Append("</div>");
                        }

                        body.Append("<div class='card'><h3>Change Password</h3><form method='post' action='/profile'>");
                        body.Append("<input type='hidden' name='action' value='changePassword'>");
                        body.Append("<label>Current Password:</label><input type='password' name='currentPassword' required>");
                        body.Append("<label>New Password:</label><input type='password' name='newPassword' required>");
                        body.Append("<input type='submit' value='Change Password'></form></div>");
                        responseString = RenderPage("My Profile", body.ToString(), user, message, messageType);
                    }
                    else
                    {
                        string action = formData["action"];

                        if (action == "updateProfile")
                        {
                            string firstName = formData["firstName"];
                            string lastName = formData["lastName"];

                            if (!string.IsNullOrEmpty(firstName) && !string.IsNullOrEmpty(lastName))
                            {
                                using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                                {
                                    conn.Open();
                                    using (SqlCommand cmd = new SqlCommand("UPDATE Users SET FirstName = @FirstName, LastName = @LastName WHERE UserID = @UserID", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@FirstName", firstName);
                                        cmd.Parameters.AddWithValue("@LastName", lastName);
                                        cmd.Parameters.AddWithValue("@UserID", user.UserID);
                                        cmd.ExecuteNonQuery();
                                    }
                                }

                                user.FirstName = firstName;
                                user.LastName = lastName;
                                _sessions[request.Cookies["session_id"].Value] = user;

                                isRedirect = true;
                                response.RedirectLocation = "/profile?msg=Profile+updated+successfully.&type=success";
                            }
                            else
                            {
                                isRedirect = true;
                                response.RedirectLocation = "/profile?edit=true&msg=First+name+and+last+name+are+required.&type=error";
                            }
                        }
                        else if (action == "removePicture")
                        {
                            if (!string.IsNullOrEmpty(user.ProfilePicturePath) && File.Exists("uploads/" + user.ProfilePicturePath))
                            {
                                File.Delete("uploads/" + user.ProfilePicturePath);
                            }

                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("UPDATE Users SET ProfilePicturePath = NULL WHERE UserID = @UserID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@UserID", user.UserID);
                                    cmd.ExecuteNonQuery();
                                }
                            }

                            user.ProfilePicturePath = null;
                            _sessions[request.Cookies["session_id"].Value] = user;

                            isRedirect = true;
                            response.RedirectLocation = "/profile?edit=true&msg=Profile+picture+removed+successfully.&type=success";
                        }
                        else if (action == "updatePicture")
                        {
                            isRedirect = true;
                            response.RedirectLocation = "/profile?edit=true&msg=Picture+upload+not+implemented.&type=error";
                        }
                        else if (action == "changePassword")
                        {
                            string currentPassword = formData["currentPassword"];
                            string newPassword = formData["newPassword"];
                            string savedHash = "";
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("SELECT PasswordHash FROM Users WHERE UserID = @UserID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@UserID", user.UserID);
                                    savedHash = cmd.ExecuteScalar().ToString();
                                }
                            }
                            if (VerifyPassword(savedHash, currentPassword))
                            {
                                string passwordValidationError = null;
                                Dictionary<string, string> passwordSettings = new Dictionary<string, string>();
                                using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                                {
                                    conn.Open();
                                    using (SqlCommand cmd = new SqlCommand("SELECT ConfigKey, ConfigValue FROM SystemConfiguration WHERE ConfigKey IN ('PasswordMinLength', 'PasswordRequireUppercase', 'PasswordRequireNumber', 'PasswordRequireSpecial')", conn))
                                    {
                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                passwordSettings[reader["ConfigKey"].ToString()] = reader["ConfigValue"].ToString();
                                            }
                                        }
                                    }
                                }

                                int minLength = passwordSettings.ContainsKey("PasswordMinLength") ? int.Parse(passwordSettings["PasswordMinLength"]) : 8;
                                bool requireUppercase = passwordSettings.ContainsKey("PasswordRequireUppercase") && passwordSettings["PasswordRequireUppercase"] == "true";
                                bool requireNumber = passwordSettings.ContainsKey("PasswordRequireNumber") && passwordSettings["PasswordRequireNumber"] == "true";
                                bool requireSpecial = passwordSettings.ContainsKey("PasswordRequireSpecial") && passwordSettings["PasswordRequireSpecial"] == "true";

                                if (newPassword.Length < minLength)
                                    passwordValidationError = "Password must be at least " + minLength + " characters long.";
                                else if (requireUppercase)
                                {
                                    bool hasUpper = false;
                                    foreach (char c in newPassword) { if (char.IsUpper(c)) { hasUpper = true; break; } }
                                    if (!hasUpper) passwordValidationError = "Password must contain at least one uppercase letter.";
                                }
                                if (passwordValidationError == null && requireNumber)
                                {
                                    bool hasNumber = false;
                                    foreach (char c in newPassword) { if (char.IsDigit(c)) { hasNumber = true; break; } }
                                    if (!hasNumber) passwordValidationError = "Password must contain at least one number.";
                                }
                                if (passwordValidationError == null && requireSpecial)
                                {
                                    bool hasSpecial = false;
                                    foreach (char c in newPassword) { if (!char.IsLetterOrDigit(c)) { hasSpecial = true; break; } }
                                    if (!hasSpecial) passwordValidationError = "Password must contain at least one special character.";
                                }

                                if (passwordValidationError != null)
                                {
                                    isRedirect = true;
                                    response.RedirectLocation = "/profile?msg=" + passwordValidationError.Replace(" ", "+") + "&type=error";
                                }
                                else
                                {
                                    string newHash = HashPassword(newPassword);
                                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                                    {
                                        conn.Open();
                                        using (SqlCommand cmd = new SqlCommand("UPDATE Users SET PasswordHash = @NewHash WHERE UserID = @UserID", conn))
                                        {
                                            cmd.Parameters.AddWithValue("@NewHash", newHash);
                                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                                            cmd.ExecuteNonQuery();
                                        }
                                    }
                                    isRedirect = true;
                                    response.RedirectLocation = "/profile?msg=Password+updated+successfully.&type=success";
                                }
                            }
                            else
                            {
                                isRedirect = true;
                                response.RedirectLocation = "/profile?msg=Incorrect+current+password.&type=error";
                            }
                        }
                    }
                }
                else
                {
                    responseString = RenderPage("Not Found", "<h1>404 - Page Not Found</h1><p>The page you are looking for does not exist or you do not have permission to access it.</p>", user, null, null);
                    response.StatusCode = 404;
                }
            }

        exit1:
            if (isRedirect && response != null && response.OutputStream.CanWrite)
            {
                response.StatusCode = 302;
            }
            else if (response != null && response.OutputStream.CanWrite)
            {
                byte[] buffer = Encoding.UTF8.GetBytes(responseString);
                response.ContentLength64 = buffer.Length;
                response.ContentType = "text/html";
                response.ContentEncoding = Encoding.UTF8;
                response.OutputStream.Write(buffer, 0, buffer.Length);
            }
            if (response != null)
            {
                response.OutputStream.Close();
            }
        }
    }
}
